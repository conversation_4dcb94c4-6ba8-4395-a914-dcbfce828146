# Avantiq Billing Microservice

A comprehensive billing system built with Spring Boot, gRPC, and PostgreSQL, implementing Domain-Driven Design (DDD) principles with Hexagonal Architecture.

## Table of Contents

- [Overview](#overview)
- [Architecture](#architecture)
- [Technologies](#technologies)
- [Getting Started](#getting-started)
- [API Documentation](#api-documentation)
- [Development](#development)
- [Security](#security)
- [Testing](#testing)
- [Deployment](#deployment)
- [Troubleshooting](#troubleshooting)

## Overview

The Avantiq Billing Microservice is a multi-tenant billing system that provides comprehensive functionality for:

- **Customer Management**: Customer profiles, contacts, segments, and relationships
- **Product Catalog**: Products, bundles, pricing, and product families
- **Payment Processing**: Multiple payment methods with secure storage
- **Security**: JWT-based authentication with role-based access control
- **Multi-tenancy**: Complete data isolation between tenants

## Architecture

### Hexagonal Architecture (Ports and Adapters)

The project follows a clean architecture pattern with three distinct layers:

```
┌─────────────────────────────────────────────────────────────┐
│                     Infrastructure Layer                     │
│  ┌─────────────┐  ┌─────────────┐  ┌──────────────────┐   │
│  │ gRPC        │  │ Persistence │  │ Security         │   │
│  │ Endpoints   │  │ (JPA)       │  │ (JWT/Spring)     │   │
│  └─────────────┘  └─────────────┘  └──────────────────┘   │
└────────────────────────────┬────────────────────────────────┘
                             │
┌────────────────────────────┴────────────────────────────────┐
│                    Application Layer                         │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ Application Services (Use Case Orchestration)        │   │
│  └─────────────────────────────────────────────────────┘   │
└────────────────────────────┬────────────────────────────────┘
                             │
┌────────────────────────────┴────────────────────────────────┐
│                      Domain Layer                            │
│  ┌──────────────┐  ┌──────────────┐  ┌────────────────┐   │
│  │ Entities     │  │ Value Objects│  │ Domain Services│   │
│  │              │  │              │  │                │   │
│  └──────────────┘  └──────────────┘  └────────────────┘   │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ Repository Interfaces (Ports)                        │   │
│  └─────────────────────────────────────────────────────┘   │
└──────────────────────────────────────────────────────────────┘
```

### Domain Model

Key aggregates and their relationships:

- **Customer**: Root aggregate for customer management
  - Addresses (Value Objects)
  - Contacts
  - Customer Notes
  - Customer Relationships
  
- **Product**: Root aggregate for product catalog
  - Product Families
  - Bundles
  - Prices (through Price Books)
  
- **Payment Method**: Managing customer payment options
- **User**: Authentication and authorization

## Technologies

- **Java 21**: Latest LTS version with modern language features
- **Spring Boot 3.5.0**: Application framework
- **gRPC**: High-performance RPC framework for API layer
- **PostgreSQL**: Primary database
- **Spring Data JPA**: ORM and database access
- **Spring Security**: Authentication and authorization
- **JWT**: Token-based authentication
- **Lombok**: Reducing boilerplate code
- **Docker**: Containerization
- **Gradle**: Build automation

### Code Quality Tools

- **Spotless**: Code formatting (Google Java Format)
- **Checkstyle**: Code style enforcement
- **PMD**: Static code analysis
- **SpotBugs**: Bug detection

## Getting Started

### Prerequisites

- Java 21 or higher
- Docker and Docker Compose
- PostgreSQL (if running locally without Docker)

### Quick Start with Docker

1. Clone the repository:
```bash
git clone https://github.com/your-org/avantiq-billing.git
cd avantiq-billing
```

2. Copy environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. Start the application:
```bash
make compose-up
```

The application will be available at:
- gRPC API: `localhost:9090`
- HTTP Management: `localhost:8080`

### Local Development Setup

1. Install dependencies:
```bash
./gradlew build
```

2. Set up PostgreSQL database:
```bash
# Create database
createdb avantiq_billing

# Run migrations
./gradlew flywayMigrate
```

3. Set required environment variables:
```bash
export JWT_SECRET="your-secure-jwt-secret-minimum-32-characters"
export POSTGRES_PASSWORD="your-database-password"
```

4. Run the application:
```bash
./gradlew bootRun
```

## API Documentation

### gRPC Services

The application exposes the following gRPC services:

#### Customer Service
- `CreateCustomer`: Create a new customer
- `UpdateCustomer`: Update customer details
- `GetCustomer`: Retrieve customer by ID
- `GetCustomerByEmail`: Retrieve customer by email
- `LinkCustomerRelationship`: Create customer relationships
- `AddPaymentMethod`: Add payment method to customer
- `AddContact`: Add contact to customer
- `ListContacts`: List customer contacts
- `UpdateContact`: Update contact details
- `ListSegments`: List available customer segments

#### Product Service
- `CreateProduct`: Create a new product
- `UpdateProduct`: Update product details
- `GetProduct`: Retrieve product by ID
- `ListProducts`: List products with pagination
- `SearchProducts`: Search products by criteria

#### Authentication Service
- `Login`: Authenticate user and receive JWT token
- `ValidateToken`: Validate JWT token
- `RefreshToken`: Refresh expired token

### Example gRPC Client (Java)

```java
// Create channel
ManagedChannel channel = ManagedChannelBuilder
    .forAddress("localhost", 9090)
    .usePlaintext()
    .build();

// Create stub with authentication
CustomerServiceGrpc.CustomerServiceBlockingStub stub = CustomerServiceGrpc
    .newBlockingStub(channel)
    .withCallCredentials(new BearerToken(jwtToken));

// Make request
CreateCustomerRequest request = CreateCustomerRequest.newBuilder()
    .setFirstName("John")
    .setLastName("Doe")
    .setEmail("<EMAIL>")
    .build();

CustomerResponse response = stub.createCustomer(request);
```

## Development

### Building the Project

```bash
# Full build with tests
./gradlew build

# Build without tests
./gradlew build -x test

# Generate protobuf classes
./gradlew generateProto
```

### Code Quality

```bash
# Format code
./gradlew spotlessApply

# Run all checks
./gradlew check

# Run specific checks
./gradlew checkstyleMain
./gradlew pmdMain
./gradlew spotbugsMain
```

### Database Migrations

```bash
# Create new migration
touch src/main/resources/db/migration/V{version}__{description}.sql

# Run migrations
./gradlew flywayMigrate

# View migration status
./gradlew flywayInfo
```

## Security

### Authentication

The system implements comprehensive JWT-based authentication with Spring Security:

```java
// Login to get JWT token
LoginRequest loginRequest = LoginRequest.newBuilder()
    .setUsername("<EMAIL>")
    .setPassword("password")
    .build();

LoginResponse loginResponse = authStub.login(loginRequest);
String jwtToken = loginResponse.getToken();
```

Tokens must be included in gRPC metadata:

```
Authorization: Bearer <jwt-token>
```

### Role-Based Access Control (RBAC)

Four distinct roles with hierarchical permissions:

| Role | Description | Key Permissions |
|------|-------------|-----------------|
| TENANT_ADMIN | Full tenant access | All operations within tenant |
| SEGMENT_ADMIN | Segment management | Manage assigned segments |
| USER | Standard user | Read/write in assigned segments |
| READONLY | Read-only access | View data only |

### Multi-Tenant Isolation

- **Tenant Isolation**: All database queries automatically filtered by tenant ID
- **Segment Isolation**: Users can only access assigned segments
- **Context Propagation**: Tenant/user context flows through all layers
- **Repository Protection**: Tenant checks enforced at data access layer

### Security Features

- **Rate Limiting**: In-memory sliding window rate limiter
  - Default: 100 requests per minute per IP
  - Configurable via properties
- **CORS Configuration**: Customizable allowed origins and methods
- **Password Security**: BCrypt with cost factor 12
- **JWT Security**:
  - Token expiration (default: 24 hours)
  - Tenant and segment claims included
  - Signature validation on every request
- **Security Headers**: 
  - X-Frame-Options: DENY
  - X-Content-Type-Options: nosniff
  - HSTS enabled
- **Input Validation**: Bean validation at all entry points
- **SQL Injection Protection**: JPA parameterized queries
- **Exception Handling**: Security errors masked from clients

### Security Configuration

#### Required Environment Variables

```bash
# JWT Configuration (Required - no defaults)
JWT_SECRET=<minimum-32-character-secret>

# Database Security
POSTGRES_PASSWORD=<secure-database-password>
POSTGRES_USER=postgres
DB_SSL_MODE=require  # Use 'require' for production

# Optional Security Settings
SECURITY_RATE_LIMIT_MAX_REQUESTS=100
SECURITY_RATE_LIMIT_WINDOW_SECONDS=60
CORS_ALLOWED_ORIGINS=http://localhost:3000,https://app.example.com
```

#### Application Properties

```properties
# JWT Settings
jwt.expiration=86400000  # 24 hours in milliseconds
jwt.secret=${JWT_SECRET}

# Security Features
spring.security.require-ssl=true  # For production
server.forward-headers-strategy=native

# Rate Limiting
security.rate-limit.max-requests=${SECURITY_RATE_LIMIT_MAX_REQUESTS:100}
security.rate-limit.window-seconds=${SECURITY_RATE_LIMIT_WINDOW_SECONDS:60}
```

### Security Best Practices

1. **Never commit secrets**: Use environment variables for all sensitive data
2. **Rotate JWT secrets**: Implement key rotation strategy
3. **Monitor rate limits**: Watch for potential DoS attempts
4. **Audit logging**: Track security-sensitive operations
5. **Regular updates**: Keep dependencies updated for security patches
6. **SSL/TLS**: Always use encrypted connections in production

## Testing

### Running Tests

```bash
# All tests
./gradlew test

# Unit tests only
./gradlew test --tests "*Test"

# Integration tests only
./gradlew test --tests "*IntegrationTest"

# With coverage
./gradlew test jacocoTestReport
```

### Test Structure

```
src/test/java/
├── unit/           # Fast, isolated unit tests
├── integration/    # Database and service integration tests
└── e2e/           # End-to-end gRPC tests
```

## Deployment

### Docker Deployment

```bash
# Build Docker image
docker build -t avantiq-billing:latest .

# Run with Docker Compose
docker-compose up -d
```

### Kubernetes Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: avantiq-billing
spec:
  replicas: 3
  selector:
    matchLabels:
      app: avantiq-billing
  template:
    metadata:
      labels:
        app: avantiq-billing
    spec:
      containers:
      - name: avantiq-billing
        image: avantiq-billing:latest
        ports:
        - containerPort: 9090  # gRPC
        - containerPort: 8080  # HTTP
        env:
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: avantiq-secrets
              key: jwt-secret
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: avantiq-secrets
              key: db-password
```

### Production Considerations

1. **Database**:
   - Use connection pooling
   - Enable SSL/TLS
   - Regular backups
   - Read replicas for scaling

2. **Security**:
   - Use secrets management (Vault, K8s Secrets)
   - Enable audit logging
   - Regular security updates
   - Penetration testing

3. **Monitoring**:
   - Application metrics (Micrometer)
   - Distributed tracing (OpenTelemetry)
   - Log aggregation (ELK Stack)
   - Health checks

4. **Performance**:
   - Horizontal scaling
   - Caching layer (Redis)
   - CDN for static assets
   - Database query optimization

## Troubleshooting

### Common Issues

#### JWT Token Issues
```bash
# Error: JWT signature does not match
# Solution: Ensure JWT_SECRET matches between services
```

#### Database Connection
```bash
# Error: Connection refused
# Solution: Check PostgreSQL is running and credentials are correct
docker-compose ps  # Check if database container is running
```

#### gRPC Connection
```bash
# Error: UNAVAILABLE: io exception
# Solution: Check if application is running on correct port
netstat -an | grep 9090  # Check if port is listening
```

### Debug Mode

Enable debug logging:
```properties
# application.properties
logging.level.com.avantiq.billing=DEBUG
logging.level.org.springframework.security=DEBUG
```

### Health Checks

Check application health:
```bash
curl http://localhost:8080/actuator/health
```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Coding Standards

- Follow Google Java Style Guide
- Write unit tests for new features
- Update documentation
- Run code quality checks before committing

## License

This project is proprietary software. All rights reserved.

## Support

For issues and questions:
- Create an issue in the repository
- Contact the development <NAME_EMAIL>