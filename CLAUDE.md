# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Java Spring Boot billing microservice implementing Domain-Driven Design (DDD) with Hexagonal Architecture patterns, integrated with gRPC for service communication. The system handles customer management, product catalogs, pricing, and billing operations.

## Build and Development Commands

### Core Build Commands
```bash
# Build the project
./gradlew build

# Clean build artifacts
./gradlew clean

# Run the application
./gradlew bootRun

# Run tests
./gradlew test

# Generate protocol buffer classes
./gradlew generateProto
```

### Code Quality and Formatting
```bash
# Run all code quality checks
./gradlew check

# Format code with Spotless (Google Java Format)
./gradlew spotlessApply

# Run Checkstyle (v10.25.0)
./gradlew checkstyleMain

# Run PMD analysis (v7.14.0)
./gradlew pmdMain

# Run SpotBugs with security plugin
./gradlew spotbugsMain
```

### Docker and Deployment
```bash
# Build and run with Docker Compose
make compose-up

# Build package only
make package
```

### Testing Commands
```bash
# Run specific test class
./gradlew test --tests "CustomerServiceTest"

# Run tests with coverage
./gradlew test jacocoTestReport

# Run only unit tests
./gradlew test --tests "*Test"

# Run only integration tests  
./gradlew test --tests "*IntegrationTest"

# Run tests with continuous mode (re-run on changes)
./gradlew test --continuous
```

## Architecture Overview

### Hexagonal Architecture (Ports and Adapters)
The project follows strict layered architecture:

- **Domain Layer**: Core business logic in `domain/` packages
  - Contains entities, value objects, domain services, and repository interfaces
  - Pure business logic without infrastructure dependencies
  
- **Application Layer**: Use case orchestration in `application/` packages  
  - Application services that coordinate domain objects
  - Transaction boundaries and workflow orchestration
  
- **Infrastructure Layer**: External adapters in `infrastructure/` packages
  - `grpc/`: gRPC endpoints and configuration
  - `persistence/`: JPA entities, repositories, and mappers
  - `rest/`: REST controllers (if any)

### Domain-Driven Design Patterns

**Aggregate Roots**: Customer, Product, Bundle, PriceBook, User
**Value Objects**: Address, Price, Metadata
**Domain Services**: CustomerDomainService, ProductDomainService, PricingService
**Repository Pattern**: Domain repository interfaces with infrastructure implementations
**Security Context**: Multi-tenant isolation with JWT-based authentication

### Key Architectural Rules

1. **Layer Dependencies**: Domain → Application → Infrastructure (never reverse)
2. **Repository Access**: Only through domain repository interfaces, never direct JPA repositories
3. **Business Logic**: Lives in domain entities and domain services
4. **Transaction Management**: Handled at application service level
5. **Exception Handling**: Domain exceptions propagated through all layers

## gRPC Integration

### Service Definitions
- `customer.proto`: Customer management operations
- `product.proto`: Product catalog and pricing operations
- `authentication.proto`: JWT-based authentication service

### Key gRPC Components
- **Endpoints**: `*GrpcEndpoint` classes in `infrastructure.grpc`
- **Configuration**: `GrpcConfig` for interceptors and global settings
- **Exception Handling**: `GrpcExceptionHandler` maps domain exceptions to gRPC status codes
- **Mappers**: Convert between domain models and protobuf messages

### gRPC Development Guidelines
- Always use application services from gRPC endpoints
- Handle exceptions with the global exception handler
- Use proper status codes for different error types
- Implement proper input validation
- Include JWT authentication in metadata: `Authorization: Bearer <token>`
- Use lite runtime for protobuf (configured in build.gradle)
- Protocol buffer classes are auto-generated in `build/generated/source/proto`

## Database and Persistence

### Technology Stack
- **Database**: PostgreSQL
- **ORM**: Spring Data JPA with Hibernate
- **Connection**: Environment-based configuration (DB_USERNAME, DB_PASSWORD)

### Entity Mapping Pattern
- Domain models separate from JPA entities
- Mappers convert between domain and persistence models
- JPA entities in `infrastructure.persistence.*.entity` packages
- Domain models in `domain.*.model` packages

### Repository Implementation
```
Domain Repository Interface → Repository Implementation → JPA Repository
CustomerRepository → CustomerRepositoryImpl → CustomerJpaRepository
```

## Testing Strategy

### Test Types
- **Unit Tests**: Domain logic and service classes (`*Test.java`)
- **Integration Tests**: gRPC endpoints and repository layer (`*IntegrationTest.java`)
- **Mapper Tests**: Entity conversion logic (`*MapperTest.java`)

### Testing Guidelines
- Mock dependencies at service boundaries
- Use H2 for repository integration tests
- Test exception scenarios thoroughly
- Follow AAA pattern (Arrange, Act, Assert)

## Code Quality Standards

### Static Analysis Tools
- **Checkstyle**: Code style and conventions (v10.25.0)
- **PMD**: Code quality and best practices (v7.14.0) 
- **SpotBugs**: Bug detection and security issues (with FindSecBugs plugin)
- **Spotless**: Code formatting with Google Java Format

### Key Quality Rules
- All public methods must have input validation
- Use Optional for potentially null values
- Follow DDD naming conventions
- Implement proper exception handling
- Add comprehensive logging at service boundaries
- Never commit secrets or sensitive data
- Use JWT for authentication, never plain text passwords

## Development Guidelines

### Adding New Features
1. Start with domain model design
2. Create domain repository interface
3. Implement application service
4. Add infrastructure adapters (gRPC, persistence)
5. Write comprehensive tests
6. Update protobuf definitions if needed

### Common Patterns
- **Service Layer**: Application services coordinate, domain services contain business logic
- **Repository Pattern**: Always implement domain repository interface
- **Exception Handling**: Use domain exceptions, let infrastructure map them
- **Validation**: Input validation at application service level
- **Transactions**: Use `@Transactional` on application service methods

### Error Handling
- Domain exceptions for business rule violations
- Infrastructure exceptions mapped to appropriate gRPC status codes
- Global exception handler for consistent error responses
- Proper logging at all exception points

## Environment Configuration

### Required Environment Variables
- `JWT_SECRET`: JWT signing secret (REQUIRED - minimum 32 characters)
- `DB_USERNAME`: PostgreSQL username (default: postgres)
- `DB_PASSWORD`: PostgreSQL password (default: empty)

### Optional Environment Variables
- `DB_SSL_MODE`: PostgreSQL SSL mode (default: prefer)
- `JWT_EXPIRATION`: JWT token expiration in milliseconds (default: 86400000 = 24 hours)
- `RATE_LIMIT_MAX_REQUESTS`: Rate limiting max requests (default: 100)
- `RATE_LIMIT_WINDOW_SECONDS`: Rate limiting window in seconds (default: 60)
- `GRPC_PORT`: gRPC server port (default: 9090)
- `CORS_ALLOWED_ORIGINS`: CORS allowed origins (default: http://localhost:3000)

### Application Properties
- Database URL: `*************************************************=${DB_SSL_MODE:prefer}`
- JPA DDL: `update` mode for development
- Management endpoints exposed: health, info
- Rate limiting: 100 requests per minute per IP by default

## Dependencies and Versions

- **Java**: 21
- **Spring Boot**: 3.5.0
- **gRPC**: 1.63.0
- **Protocol Buffers**: 3.25.1 (protoc: 3.21.12, grpc-java: 1.58.0)
- **PostgreSQL**: Latest compatible driver
- **Spring Security**: OAuth2 Resource Server
- **JWT**: io.jsonwebtoken 0.11.5
- **Lombok**: 1.18.30
- **Gradle**: 8.14

## Claude Development Rules

**MUST FOLLOW these 7 rules when working on this codebase:**

1. **Layer Dependency Rule**: Never allow infrastructure or application layers to leak into domain layer. Domain must remain pure business logic without external dependencies.

2. **Repository Pattern Rule**: Always access data through domain repository interfaces. Never inject or use JPA repositories directly in application or domain services.

3. **Transaction Boundary Rule**: Place `@Transactional` annotations only on application service methods, never on domain services or repository implementations.

4. **Exception Propagation Rule**: Use domain-specific exceptions that extend `DomainException`. Let infrastructure layers map these to appropriate response formats (gRPC status, HTTP status).

5. **Testing Isolation Rule**: Unit tests must mock all external dependencies. Integration tests should test the full stack but use test-specific configurations (H2 database, test containers).

6. **Code Quality Gate Rule**: Always run `./gradlew spotlessApply` before committing code changes. All code must pass Checkstyle, PMD, and SpotBugs checks.

7. **gRPC Implementation Rule**: gRPC endpoints must only call application services, never domain services or repositories directly. Use mappers to convert between protobuf messages and domain models.

8. **Planning Rule**: Before making any changes, first think through the problem, read the codebase for relevant files, and write a detailed plan to `docs/tasks/todo.md`.

9. **Task Documentation Rule**: The plan must have a clear list of todo items with checkboxes that can be marked off as work progresses.

10. **Approval Rule**: Before beginning any work, check in with the user to verify and approve the plan.

11. **Progress Communication Rule**: Work on todo items incrementally, marking them as complete as you go, and provide high-level explanations of changes at every step.

12. **Simplicity Rule**: Make every task and code change as simple as possible. Avoid massive or complex changes. Every change should impact as little code as possible. Everything is about simplicity.

13. **Review Documentation Rule**: After completing work, add a review section to the `docs/tasks/todo.md` file with a summary of changes made and any other relevant information.

## Important Notes

- The project uses Java 21 language features (Gradle 8.14)
- Protocol buffer generation happens automatically during build
- All gRPC services use lite runtime for better performance
- Code formatting is enforced via Spotless with Google Java Format
- Transaction management is declarative using Spring's `@Transactional`
- Multi-tenant architecture with complete data isolation
- JWT authentication required for all API access
- Rate limiting enabled by default (100 requests/minute per IP)
- Static analysis tools configured with failure tolerance for development
- Main class: `com.avantiq.billing.Application`
- Generated protobuf files excluded from formatting