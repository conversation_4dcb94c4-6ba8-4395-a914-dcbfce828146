package com.avantiq.billing.domain.product.valueobject;

import com.avantiq.billing.domain.product.model.Price;
import java.math.BigDecimal;

/** Base interface for all pricing strategies. Implementations should be immutable value objects. */
public interface Pricing {

  /**
   * Gets the pricing strategy type.
   *
   * @return the pricing strategy
   */
  Price.PriceStrategy getStrategy();

  /**
   * Calculates the total amount based on the given quantity.
   *
   * @param quantity the quantity to price
   * @return the calculated amount
   */
  BigDecimal calculateAmount(BigDecimal quantity);

  /**
   * Validates the pricing configuration.
   *
   * @throws IllegalArgumentException if the configuration is invalid
   */
  void validate();

  /**
   * Provides a human-readable summary of the pricing.
   *
   * @return pricing summary for UI display
   */
  PricingSummary summarize();
}
