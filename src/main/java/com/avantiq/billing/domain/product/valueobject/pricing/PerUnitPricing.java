package com.avantiq.billing.domain.product.valueobject.pricing;

import com.avantiq.billing.domain.product.model.Price;
import com.avantiq.billing.domain.product.valueobject.Pricing;
import com.avantiq.billing.domain.product.valueobject.PricingSummary;
import com.fasterxml.jackson.annotation.JsonTypeName;
import java.math.BigDecimal;
import java.math.RoundingMode;
import lombok.Builder;
import lombok.NonNull;
import lombok.Value;

/**
 * Per-unit pricing with optional block/chunk pricing support. Can charge per individual unit or per
 * block of units.
 */
@Value
@Builder
@JsonTypeName("PER_UNIT")
public class PerUnitPricing implements Pricing {

  @NonNull BigDecimal pricePerUnit;
  @NonNull String pricingUnit; // e.g., "request", "GB", "hour", "user"
  BigDecimal blockSize; // Optional, e.g., 1000000 for "per million"
  @Builder.Default RoundingMode blockRounding = RoundingMode.CEILING;

  @Override
  public Price.PriceStrategy getStrategy() {
    return Price.PriceStrategy.PER_UNIT;
  }

  @Override
  public BigDecimal calculateAmount(BigDecimal quantity) {
    if (quantity == null || quantity.compareTo(BigDecimal.ZERO) < 0) {
      throw new IllegalArgumentException("Quantity must be non-negative");
    }

    if (blockSize != null && blockSize.compareTo(BigDecimal.ZERO) > 0) {
      // Calculate number of blocks (e.g., per million requests)
      BigDecimal blocks = quantity.divide(blockSize, 0, blockRounding);
      return blocks.multiply(pricePerUnit);
    }

    // Simple per-unit pricing
    return quantity.multiply(pricePerUnit);
  }

  @Override
  public void validate() {
    if (pricePerUnit.compareTo(BigDecimal.ZERO) < 0) {
      throw new IllegalArgumentException("Price per unit must be non-negative");
    }
    if (pricingUnit == null || pricingUnit.trim().isEmpty()) {
      throw new IllegalArgumentException("Pricing unit must be specified");
    }
    if (blockSize != null && blockSize.compareTo(BigDecimal.ZERO) <= 0) {
      throw new IllegalArgumentException("Block size must be positive if specified");
    }
  }

  @Override
  public PricingSummary summarize() {
    if (blockSize != null) {
      String blockDescription = formatBlockSize(blockSize);
      return PricingSummary.builder()
          .description("$" + pricePerUnit + " per " + blockDescription + " " + pricingUnit)
          .formula("ceil(quantity / " + blockSize + ") × $" + pricePerUnit)
          .details("Charged in blocks of " + blockDescription)
          .build();
    }

    return PricingSummary.builder()
        .description("$" + pricePerUnit + " per " + pricingUnit)
        .formula("quantity × $" + pricePerUnit)
        .build();
  }

  private String formatBlockSize(BigDecimal size) {
    if (size.compareTo(new BigDecimal("1000000")) == 0) {
      return "million";
    } else if (size.compareTo(new BigDecimal("1000")) == 0) {
      return "thousand";
    } else if (size.compareTo(new BigDecimal("100")) == 0) {
      return "hundred";
    }
    return size.toString();
  }
}
