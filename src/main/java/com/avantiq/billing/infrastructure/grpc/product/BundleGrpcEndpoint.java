package com.avantiq.billing.infrastructure.grpc.product;

import com.avantiq.billing.application.product.BundleApplicationService;
import com.avantiq.billing.domain.product.exception.InvalidProductDataException;
import com.avantiq.billing.domain.product.exception.ProductNotFoundException;
import com.avantiq.billing.domain.product.model.Bundle;
import com.avantiq.billing.domain.product.model.BundleProduct;
import com.avantiq.billing.infrastructure.grpc.common.GrpcExceptionHandler;
import com.avantiq.billing.infrastructure.grpc.mapper.BundleMapper;
import com.avantiq.billing.product.grpc.AddProductToBundleRequest;
import com.avantiq.billing.product.grpc.BundleProductResponse;
import com.avantiq.billing.product.grpc.BundleResponse;
import com.avantiq.billing.product.grpc.BundleServiceGrpc;
import com.avantiq.billing.product.grpc.CreateBundleRequest;
import com.avantiq.billing.product.grpc.DeleteBundleProductResponse;
import com.avantiq.billing.product.grpc.DeleteBundleRequest;
import com.avantiq.billing.product.grpc.DeleteBundleResponse;
import com.avantiq.billing.product.grpc.GetBundleRequest;
import com.avantiq.billing.product.grpc.ListBundlesRequest;
import com.avantiq.billing.product.grpc.ListBundlesResponse;
import com.avantiq.billing.product.grpc.RemoveProductFromBundleRequest;
import com.avantiq.billing.product.grpc.UpdateBundleRequest;
import io.grpc.stub.StreamObserver;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class BundleGrpcEndpoint extends BundleServiceGrpc.BundleServiceImplBase {

  private final BundleApplicationService bundleApplicationService;

  @Autowired
  public BundleGrpcEndpoint(BundleApplicationService bundleApplicationService) {
    this.bundleApplicationService = bundleApplicationService;
  }

  @Override
  public void createBundle(
      CreateBundleRequest request, StreamObserver<BundleResponse> responseObserver) {
    try {
      // Map request to domain model
      Bundle bundle = BundleMapper.toDomain(request);

      // Validate bundle data
      if (bundle.getName() == null || bundle.getName().trim().isEmpty()) {
        throw new InvalidProductDataException("Bundle name is required");
      }

      // Create bundle
      Bundle createdBundle = bundleApplicationService.createBundle(bundle);

      // Map domain model to response
      BundleResponse response = BundleMapper.toProto(createdBundle);
      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (InvalidProductDataException e) {
      log.error("Error in createBundle", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    } catch (Exception e) {
      log.error("Unexpected error in createBundle", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }

  @Override
  public void getBundle(GetBundleRequest request, StreamObserver<BundleResponse> responseObserver) {
    try {
      String bundleId = request.getId();

      // Validate bundle ID
      if (bundleId == null || bundleId.trim().isEmpty()) {
        throw new InvalidProductDataException("Bundle ID is required");
      }

      // Get bundle
      Optional<Bundle> bundleOpt = bundleApplicationService.getBundleById(bundleId);

      if (bundleOpt.isEmpty()) {
        throw new ProductNotFoundException(bundleId);
      }

      // Map domain model to response
      BundleResponse response = BundleMapper.toProto(bundleOpt.get());
      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (ProductNotFoundException | InvalidProductDataException e) {
      log.error("Error in getBundle", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    } catch (Exception e) {
      log.error("Unexpected error in getBundle", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }

  @Override
  public void listBundles(
      ListBundlesRequest request, StreamObserver<ListBundlesResponse> responseObserver) {
    try {
      // List all bundles
      List<Bundle> bundles = bundleApplicationService.listAllBundles();

      // Map domain models to response
      ListBundlesResponse response = BundleMapper.toGrpcListResponse(bundles, new ArrayList<>());
      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (Exception e) {
      log.error("Unexpected error in listBundles", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }

  @Override
  public void updateBundle(
      UpdateBundleRequest request, StreamObserver<BundleResponse> responseObserver) {
    try {
      String bundleId = request.getId();

      // Validate bundle ID
      if (bundleId == null || bundleId.trim().isEmpty()) {
        throw new InvalidProductDataException("Bundle ID is required");
      }

      // Check if bundle exists
      Optional<Bundle> existingBundleOpt = bundleApplicationService.getBundleById(bundleId);

      if (existingBundleOpt.isEmpty()) {
        throw new ProductNotFoundException(bundleId);
      }

      // Map request to domain model
      Bundle bundle = BundleMapper.toDomain(request, existingBundleOpt.get().getVersion());

      // Validate bundle data
      if (bundle.getName() == null || bundle.getName().trim().isEmpty()) {
        throw new InvalidProductDataException("Bundle name is required");
      }

      // Update bundle
      Bundle updatedBundle = bundleApplicationService.updateBundle(bundle);

      // Map domain model to response
      BundleResponse response = BundleMapper.toProto(updatedBundle);
      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (ProductNotFoundException | InvalidProductDataException e) {
      log.error("Error in updateBundle", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    } catch (Exception e) {
      log.error("Unexpected error in updateBundle", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }

  @Override
  public void deleteBundle(
      DeleteBundleRequest request, StreamObserver<DeleteBundleResponse> responseObserver) {
    try {
      String bundleId = request.getId();

      // Validate bundle ID
      if (bundleId == null || bundleId.trim().isEmpty()) {
        throw new InvalidProductDataException("Bundle ID is required");
      }

      // Check if bundle exists
      Optional<Bundle> existingBundleOpt = bundleApplicationService.getBundleById(bundleId);

      if (existingBundleOpt.isEmpty()) {
        throw new ProductNotFoundException(bundleId);
      }

      // Delete bundle
      bundleApplicationService.deleteBundle(bundleId);

      // Map domain model to response
      DeleteBundleResponse response = DeleteBundleResponse.newBuilder().setSuccess(true).build();
      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (ProductNotFoundException | InvalidProductDataException e) {
      log.error("Error in deleteBundle", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    } catch (Exception e) {
      log.error("Unexpected error in deleteBundle", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }

  @Override
  public void addProductToBundle(
      AddProductToBundleRequest request, StreamObserver<BundleProductResponse> responseObserver) {
    try {
      String bundleId = request.getBundleId();
      String productId = request.getProductId();

      // Validate request
      if (bundleId == null || bundleId.trim().isEmpty()) {
        throw new InvalidProductDataException("Bundle ID is required");
      }

      if (productId == null || productId.trim().isEmpty()) {
        throw new InvalidProductDataException("Product ID is required");
      }

      // Check if bundle exists
      Optional<Bundle> existingBundleOpt = bundleApplicationService.getBundleById(bundleId);

      if (existingBundleOpt.isEmpty()) {
        throw new ProductNotFoundException("Bundle not found: " + bundleId);
      }

      // Add product to bundle
      BundleProduct bundleProduct =
          bundleApplicationService.addProductToBundle(
              bundleId, productId, request.getQuantity(), request.getOptionalFlag());

      // Map domain model to response
      BundleProductResponse response = BundleMapper.toProto(bundleProduct);
      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (ProductNotFoundException | InvalidProductDataException e) {
      log.error("Error in addProductToBundle", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    } catch (Exception e) {
      log.error("Unexpected error in addProductToBundle", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }

  @Override
  public void removeProductFromBundle(
      RemoveProductFromBundleRequest request,
      StreamObserver<DeleteBundleProductResponse> responseObserver) {
    try {
      String bundleProductId = request.getBundleProductId();

      // Validate request
      if (bundleProductId == null || bundleProductId.trim().isEmpty()) {
        throw new InvalidProductDataException("Bundle product ID is required");
      }

      // Remove product from bundle
      bundleApplicationService.removeProductFromBundle(bundleProductId);

      // Map domain model to response
      DeleteBundleProductResponse response =
          DeleteBundleProductResponse.newBuilder().setSuccess(true).build();
      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (InvalidProductDataException e) {
      log.error("Error in removeProductFromBundle", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    } catch (Exception e) {
      log.error("Unexpected error in removeProductFromBundle", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }
}
