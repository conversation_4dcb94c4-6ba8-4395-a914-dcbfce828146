package com.avantiq.billing.infrastructure.grpc.product;

import com.avantiq.billing.application.product.ProductApplicationService;
import com.avantiq.billing.domain.product.exception.InvalidProductDataException;
import com.avantiq.billing.domain.product.exception.ProductNotFoundException;
import com.avantiq.billing.infrastructure.grpc.common.GrpcExceptionHandler;
import com.avantiq.billing.infrastructure.grpc.mapper.ProductMapper;
import com.avantiq.billing.product.grpc.*;
import io.grpc.stub.StreamObserver;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

@Service
@Component
@Slf4j
public class ProductGrpcEndpoint extends ProductServiceGrpc.ProductServiceImplBase {

  private final ProductApplicationService productApplicationService;

  @Autowired
  public ProductGrpcEndpoint(ProductApplicationService productApplicationService) {
    this.productApplicationService = productApplicationService;
  }

  @Override
  public void createProduct(
      CreateProductRequest request, StreamObserver<ProductResponse> responseObserver) {
    try {
      // Map request to domain model
      var product = ProductMapper.toDomain(request);

      // Validate product data
      if (product.getName() == null || product.getName().trim().isEmpty()) {
        throw new InvalidProductDataException("Product name is required");
      }

      var createdProduct = productApplicationService.createProduct(product);

      // Map domain model to response
      var response = ProductMapper.toProto(createdProduct);
      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (InvalidProductDataException e) {
      log.error("Error in createProduct", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    } catch (Exception e) {
      log.error("Unexpected error in createProduct", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }

  @Override
  public void getProduct(
      GetProductRequest request, StreamObserver<ProductResponse> responseObserver) {
    try {
      String productId = request.getId();

      // Validate product ID
      if (productId == null || productId.trim().isEmpty()) {
        throw new InvalidProductDataException("Product ID is required");
      }

      Optional<com.avantiq.billing.domain.product.model.Product> productOpt =
          productApplicationService.getProductById(productId);

      if (productOpt.isEmpty()) {
        throw new ProductNotFoundException(productId);
      }

      var response = ProductMapper.toProto(productOpt.get());
      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (ProductNotFoundException | InvalidProductDataException e) {
      log.error("Error in getProduct", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    } catch (Exception e) {
      log.error("Unexpected error in getProduct", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }

  @Override
  public void listProducts(
      ListProductsRequest request, StreamObserver<ListProductsResponse> responseObserver) {
    try {
      var products = productApplicationService.listAllProducts();
      var response = ProductMapper.toGrpcListResponse(products);
      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (Exception e) {
      log.error("Unexpected error in listProducts", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }
}
