package com.avantiq.billing.infrastructure.grpc.mapper;

import com.avantiq.billing.customer.grpc.Segment;
import com.avantiq.billing.domain.customer.model.Address;
import com.avantiq.billing.domain.customer.model.Customer;
import com.avantiq.billing.domain.customer.model.CustomerSegment;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CustomerMapper {

  private CustomerMapper() {}

  public static Customer toDomain(com.avantiq.billing.customer.grpc.CreateCustomerRequest request) {
    try {
      UUID id = UUID.randomUUID();
      UUID segmentId =
          request.getSegmentId().isEmpty() ? null : UUID.fromString(request.getSegmentId());

      // Create customer with the required constructor parameters
      // TODO: Extract tenantId from JWT token or gRPC metadata for proper multi-tenant isolation
      Customer customer =
          new Customer(
              id,
              request.getFirstName(),
              request.getLastName(),
              request.getEmail(),
              request.getCountry(),
              segmentId,
              Customer.Status.ACTIVE,
              1L); // Default tenant for now

      // Set additional fields
      customer.setCompanyName(request.getCompanyName());
      customer.setVatNumber(request.getVatNumber());

      // Map repeated addresses from proto to AddressDomain list
      List<Address> addresses =
          request.getAddressesList().stream()
              .map(
                  protoAddr -> {
                    Address entity = new Address();
                    entity.setStreet(protoAddr.getStreet());
                    entity.setCity(protoAddr.getCity());
                    entity.setState(protoAddr.getState());
                    entity.setPostalCode(protoAddr.getPostalCode());
                    entity.setCountry(protoAddr.getCountry());
                    entity.setAddressType(
                        Address.AddressType.valueOf(protoAddr.getAddressType().name()));
                    return entity;
                  })
              .toList();
      customer.setAddresses(addresses);
      return customer;
    } catch (Exception e) {
      log.error("Error in fromProto", e);
      return null;
    }
  }

  public static void updateDomainFromProto(
      com.avantiq.billing.customer.grpc.UpdateCustomerRequest request, Customer customer) {
    try {
      // Extract the email from the existing customer since it's not in the update request
      String email = customer.getEmail();

      // Use the updateDetails method from the Customer class
      customer.updateDetails(
          request.getFirstName(),
          request.getLastName(),
          email, // Use existing email
          request.getCompanyName(),
          request.getVatNumber(),
          request.getCountry());

      // Update segmentId if provided
      if (!request.getSegmentId().isEmpty()) {
        customer.setSegmentId(UUID.fromString(request.getSegmentId()));
      }

      // Update status if provided
      String status = request.getStatus();
      if (!status.isEmpty()) {
        customer.setStatus(Customer.Status.valueOf(status.toUpperCase()));
      }

      // Update addresses if present
      if (request.getAddressesCount() > 0) {
        List<Address> addresses =
            request.getAddressesList().stream()
                .map(
                    protoAddr -> {
                      Address entity = new Address();
                      entity.setStreet(protoAddr.getStreet());
                      entity.setCity(protoAddr.getCity());
                      entity.setState(protoAddr.getState());
                      entity.setPostalCode(protoAddr.getPostalCode());
                      entity.setCountry(protoAddr.getCountry());
                      entity.setAddressType(
                          Address.AddressType.valueOf(protoAddr.getAddressType().name()));
                      return entity;
                    })
                .toList();
        customer.setAddresses(addresses);
      }
    } catch (Exception e) {
      log.error("Error in updateDomainFromProto", e);
    }
  }

  public static Segment toProto(CustomerSegment segment) {
    if (segment == null) {
      return Segment.getDefaultInstance();
    }
    return Segment.newBuilder()
        .setId(segment.getId().toString())
        .setName(segment.getName())
        // Add other fields as needed
        .build();
  }

  public static com.avantiq.billing.customer.grpc.Customer toProto(Customer domain) {
    com.avantiq.billing.customer.grpc.Customer.Builder builder =
        com.avantiq.billing.customer.grpc.Customer.newBuilder();
    // Only map addresses if they are initialized (fetched)
    if (domain.getAddresses() != null
        && org.hibernate.Hibernate.isInitialized(domain.getAddresses())
        && !domain.getAddresses().isEmpty()) {
      for (Address address : domain.getAddresses()) {
        com.avantiq.billing.customer.grpc.Address.Builder addressBuilder =
            com.avantiq.billing.customer.grpc.Address.newBuilder()
                .setStreet(address.getStreet())
                .setCity(address.getCity())
                .setState(address.getState())
                .setPostalCode(address.getPostalCode())
                .setCountry(address.getCountry());
        if (address.getAddressType() != null) {
          addressBuilder.setAddressType(
              com.avantiq.billing.customer.grpc.Address.AddressType.valueOf(
                  address.getAddressType().name()));
        }
        builder.addAddresses(addressBuilder.build());
      }
    }
    builder
        .setId(domain.getId() != null ? domain.getId().toString() : "")
        .setFirstName(domain.getFirstName() == null ? "" : domain.getFirstName())
        .setLastName(domain.getLastName() == null ? "" : domain.getLastName())
        .setEmail(domain.getEmail() == null ? "" : domain.getEmail())
        .setCompanyName(domain.getCompanyName() == null ? "" : domain.getCompanyName())
        .setVatNumber(domain.getVatNumber() == null ? "" : domain.getVatNumber())
        .setCountry(domain.getCountry() == null ? "" : domain.getCountry())
        .setSegmentId(domain.getSegmentId() != null ? domain.getSegmentId().toString() : "")
        .setStatus(domain.getStatus() != null ? domain.getStatus().name().toLowerCase() : "")
        .setCreatedAt(domain.getCreatedAt() == null ? "" : domain.getCreatedAt().toString())
        .setUpdatedAt(domain.getUpdatedAt() == null ? "" : domain.getUpdatedAt().toString());
    return builder.build();
  }

  public static com.avantiq.billing.customer.grpc.Contact toProto(
      com.avantiq.billing.domain.customer.model.Contact contact) {
    if (contact == null) {
      return com.avantiq.billing.customer.grpc.Contact.getDefaultInstance();
    }
    return com.avantiq.billing.customer.grpc.Contact.newBuilder()
        .setId(contact.getId() != null ? contact.getId().toString() : "")
        .setFirstName(contact.getFirstName() != null ? contact.getFirstName() : "")
        .setLastName(contact.getLastName() != null ? contact.getLastName() : "")
        .setEmail(contact.getEmail() != null ? contact.getEmail() : "")
        .setContactType(contact.getContactType() != null ? contact.getContactType() : "")
        .setIsDefault(contact.isDefault())
        .build();
  }
}
