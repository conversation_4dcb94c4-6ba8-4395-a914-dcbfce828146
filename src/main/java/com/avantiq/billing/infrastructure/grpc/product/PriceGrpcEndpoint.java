package com.avantiq.billing.infrastructure.grpc.product;

import com.avantiq.billing.application.product.PriceApplicationService;
import com.avantiq.billing.domain.product.exception.InvalidProductDataException;
import com.avantiq.billing.domain.product.exception.ProductNotFoundException;
import com.avantiq.billing.domain.product.model.Price;
import com.avantiq.billing.infrastructure.grpc.common.GrpcExceptionHandler;
import com.avantiq.billing.infrastructure.grpc.mapper.PriceGrpcMapper;
import com.avantiq.billing.product.grpc.CreatePriceRequest;
import com.avantiq.billing.product.grpc.DeletePriceRequest;
import com.avantiq.billing.product.grpc.DeletePriceResponse;
import com.avantiq.billing.product.grpc.GetPriceRequest;
import com.avantiq.billing.product.grpc.ListPricesRequest;
import com.avantiq.billing.product.grpc.ListPricesResponse;
import com.avantiq.billing.product.grpc.PriceResponse;
import com.avantiq.billing.product.grpc.PriceServiceGrpc;
import com.avantiq.billing.product.grpc.UpdatePriceRequest;
import io.grpc.stub.StreamObserver;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class PriceGrpcEndpoint extends PriceServiceGrpc.PriceServiceImplBase {

  private final PriceApplicationService priceApplicationService;
  private final PriceGrpcMapper priceMapper;

  @Autowired
  public PriceGrpcEndpoint(
      PriceApplicationService priceApplicationService, PriceGrpcMapper priceMapper) {
    this.priceApplicationService = priceApplicationService;
    this.priceMapper = priceMapper;
  }

  @Override
  public void createPrice(
      CreatePriceRequest request, StreamObserver<PriceResponse> responseObserver) {
    try {
      // Map request to domain model
      Price price = priceMapper.toDomain(request);

      // Validate price data
      if (price.getProductId() == null) {
        throw new InvalidProductDataException("Product ID is required");
      }

      if (price.getPriceBookId() == null) {
        throw new InvalidProductDataException("Price Book ID is required");
      }

      if (price.getPricing() == null) {
        throw new InvalidProductDataException("Pricing configuration is required");
      }

      // Create price
      Price createdPrice = priceApplicationService.createPrice(price);

      // Map domain model to response
      PriceResponse response = priceMapper.toProto(createdPrice);
      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (InvalidProductDataException e) {
      log.error("Error in createPrice", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    } catch (Exception e) {
      log.error("Unexpected error in createPrice", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }

  @Override
  public void getPrice(GetPriceRequest request, StreamObserver<PriceResponse> responseObserver) {
    try {
      String priceId = request.getId();

      // Validate price ID
      if (priceId == null || priceId.trim().isEmpty()) {
        throw new InvalidProductDataException("Price ID is required");
      }

      // Get price
      Optional<Price> priceOpt = priceApplicationService.getPriceById(priceId);

      if (priceOpt.isEmpty()) {
        throw new ProductNotFoundException("Price not found: " + priceId);
      }

      // Map domain model to response
      PriceResponse response = priceMapper.toProto(priceOpt.get());
      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (ProductNotFoundException | InvalidProductDataException e) {
      log.error("Error in getPrice", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    } catch (Exception e) {
      log.error("Unexpected error in getPrice", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }

  @Override
  public void listPrices(
      ListPricesRequest request, StreamObserver<ListPricesResponse> responseObserver) {
    try {
      List<Price> prices;

      // Check if filtering by product ID
      if (request.getProductId() != null && !request.getProductId().isEmpty()) {
        prices = priceApplicationService.listPricesByProductId(request.getProductId());
      }
      // Check if filtering by price book ID
      else if (request.getPriceBookId() != null && !request.getPriceBookId().isEmpty()) {
        prices = priceApplicationService.listPricesByPriceBookId(request.getPriceBookId());
      }
      // No filters, list all prices
      else {
        prices = priceApplicationService.listAllPrices();
      }

      // Map domain models to response
      ListPricesResponse response = priceMapper.toGrpcListResponse(prices);
      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (Exception e) {
      log.error("Unexpected error in listPrices", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }

  @Override
  public void updatePrice(
      UpdatePriceRequest request, StreamObserver<PriceResponse> responseObserver) {
    try {
      String priceId = request.getId();

      // Validate price ID
      if (priceId == null || priceId.trim().isEmpty()) {
        throw new InvalidProductDataException("Price ID is required");
      }

      // Check if price exists
      Optional<Price> existingPriceOpt = priceApplicationService.getPriceById(priceId);

      if (existingPriceOpt.isEmpty()) {
        throw new ProductNotFoundException("Price not found: " + priceId);
      }

      // Map request to domain model
      Price price = priceMapper.toDomain(request, existingPriceOpt.get().getVersion());

      // Validate price data
      if (price.getProductId() == null) {
        throw new InvalidProductDataException("Product ID is required");
      }

      if (price.getPriceBookId() == null) {
        throw new InvalidProductDataException("Price Book ID is required");
      }

      if (price.getPricing() == null) {
        throw new InvalidProductDataException("Pricing configuration is required");
      }

      // Preserve creation date
      price.setCreatedAt(existingPriceOpt.get().getCreatedAt());

      // Update price
      Price updatedPrice = priceApplicationService.updatePrice(price);

      // Map domain model to response
      PriceResponse response = priceMapper.toProto(updatedPrice);
      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (ProductNotFoundException | InvalidProductDataException e) {
      log.error("Error in updatePrice", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    } catch (Exception e) {
      log.error("Unexpected error in updatePrice", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }

  @Override
  public void deletePrice(
      DeletePriceRequest request, StreamObserver<DeletePriceResponse> responseObserver) {
    try {
      String priceId = request.getId();

      // Validate price ID
      if (priceId == null || priceId.trim().isEmpty()) {
        throw new InvalidProductDataException("Price ID is required");
      }

      // Check if price exists
      Optional<Price> existingPriceOpt = priceApplicationService.getPriceById(priceId);

      if (existingPriceOpt.isEmpty()) {
        throw new ProductNotFoundException("Price not found: " + priceId);
      }

      // Delete price
      priceApplicationService.deletePrice(priceId);

      // Map domain model to response
      DeletePriceResponse response = DeletePriceResponse.newBuilder().setSuccess(true).build();
      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (ProductNotFoundException | InvalidProductDataException e) {
      log.error("Error in deletePrice", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    } catch (Exception e) {
      log.error("Unexpected error in deletePrice", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }
}
