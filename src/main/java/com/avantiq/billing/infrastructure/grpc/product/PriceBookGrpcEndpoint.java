package com.avantiq.billing.infrastructure.grpc.product;

import com.avantiq.billing.application.product.PriceBookApplicationService;
import com.avantiq.billing.domain.product.exception.InvalidProductDataException;
import com.avantiq.billing.domain.product.exception.ProductNotFoundException;
import com.avantiq.billing.domain.product.model.PriceBook;
import com.avantiq.billing.infrastructure.grpc.common.GrpcExceptionHandler;
import com.avantiq.billing.infrastructure.grpc.mapper.PriceBookMapper;
import com.avantiq.billing.product.grpc.CreatePriceBookRequest;
import com.avantiq.billing.product.grpc.DeletePriceBookRequest;
import com.avantiq.billing.product.grpc.DeletePriceBookResponse;
import com.avantiq.billing.product.grpc.GetPriceBookRequest;
import com.avantiq.billing.product.grpc.ListPriceBooksRequest;
import com.avantiq.billing.product.grpc.ListPriceBooksResponse;
import com.avantiq.billing.product.grpc.PriceBookResponse;
import com.avantiq.billing.product.grpc.PriceBookServiceGrpc;
import com.avantiq.billing.product.grpc.UpdatePriceBookRequest;
import io.grpc.stub.StreamObserver;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class PriceBookGrpcEndpoint extends PriceBookServiceGrpc.PriceBookServiceImplBase {

  private final PriceBookApplicationService priceBookApplicationService;

  @Autowired
  public PriceBookGrpcEndpoint(PriceBookApplicationService priceBookApplicationService) {
    this.priceBookApplicationService = priceBookApplicationService;
  }

  @Override
  public void createPriceBook(
      CreatePriceBookRequest request, StreamObserver<PriceBookResponse> responseObserver) {
    try {
      // Map request to domain model
      PriceBook priceBook = PriceBookMapper.toDomain(request);

      // Validate price book data
      if (priceBook.getName() == null || priceBook.getName().trim().isEmpty()) {
        throw new InvalidProductDataException("Price book name is required");
      }

      if (priceBook.getCurrency() == null || priceBook.getCurrency().trim().isEmpty()) {
        throw new InvalidProductDataException("Currency is required");
      }

      // Create price book
      PriceBook createdPriceBook = priceBookApplicationService.createPriceBook(priceBook);

      // Map domain model to response
      PriceBookResponse response = PriceBookMapper.toProto(createdPriceBook);
      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (InvalidProductDataException e) {
      log.error("Error in createPriceBook", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    } catch (Exception e) {
      log.error("Unexpected error in createPriceBook", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }

  @Override
  public void getPriceBook(
      GetPriceBookRequest request, StreamObserver<PriceBookResponse> responseObserver) {
    try {
      String priceBookId = request.getId();

      // Validate price book ID
      if (priceBookId == null || priceBookId.trim().isEmpty()) {
        throw new InvalidProductDataException("Price book ID is required");
      }

      // Get price book
      Optional<PriceBook> priceBookOpt = priceBookApplicationService.getPriceBookById(priceBookId);

      if (priceBookOpt.isEmpty()) {
        throw new ProductNotFoundException("Price book not found: " + priceBookId);
      }

      // Map domain model to response
      PriceBookResponse response = PriceBookMapper.toProto(priceBookOpt.get());
      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (ProductNotFoundException | InvalidProductDataException e) {
      log.error("Error in getPriceBook", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    } catch (Exception e) {
      log.error("Unexpected error in getPriceBook", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }

  @Override
  public void listPriceBooks(
      ListPriceBooksRequest request, StreamObserver<ListPriceBooksResponse> responseObserver) {
    try {
      // List all price books
      List<PriceBook> priceBooks = priceBookApplicationService.listAllPriceBooks();

      // Map domain models to response
      ListPriceBooksResponse response = PriceBookMapper.toGrpcListResponse(priceBooks);
      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (Exception e) {
      log.error("Unexpected error in listPriceBooks", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }

  @Override
  public void updatePriceBook(
      UpdatePriceBookRequest request, StreamObserver<PriceBookResponse> responseObserver) {
    try {
      String priceBookId = request.getId();

      // Validate price book ID
      if (priceBookId == null || priceBookId.trim().isEmpty()) {
        throw new InvalidProductDataException("Price book ID is required");
      }

      // Check if price book exists
      Optional<PriceBook> existingPriceBookOpt =
          priceBookApplicationService.getPriceBookById(priceBookId);

      if (existingPriceBookOpt.isEmpty()) {
        throw new ProductNotFoundException("Price book not found: " + priceBookId);
      }

      // Map request to domain model
      PriceBook priceBook = PriceBookMapper.toDomain(request);

      // Validate price book data
      if (priceBook.getName() == null || priceBook.getName().trim().isEmpty()) {
        throw new InvalidProductDataException("Price book name is required");
      }

      if (priceBook.getCurrency() == null || priceBook.getCurrency().trim().isEmpty()) {
        throw new InvalidProductDataException("Currency is required");
      }

      // Update price book
      PriceBook updatedPriceBook = priceBookApplicationService.updatePriceBook(priceBook);

      // Map domain model to response
      PriceBookResponse response = PriceBookMapper.toProto(updatedPriceBook);
      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (ProductNotFoundException | InvalidProductDataException e) {
      log.error("Error in updatePriceBook", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    } catch (Exception e) {
      log.error("Unexpected error in updatePriceBook", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }

  @Override
  public void deletePriceBook(
      DeletePriceBookRequest request, StreamObserver<DeletePriceBookResponse> responseObserver) {
    try {
      String priceBookId = request.getId();

      // Validate price book ID
      if (priceBookId == null || priceBookId.trim().isEmpty()) {
        throw new InvalidProductDataException("Price book ID is required");
      }

      // Check if price book exists
      Optional<PriceBook> existingPriceBookOpt =
          priceBookApplicationService.getPriceBookById(priceBookId);

      if (existingPriceBookOpt.isEmpty()) {
        throw new ProductNotFoundException("Price book not found: " + priceBookId);
      }

      // Delete price book
      priceBookApplicationService.deletePriceBook(priceBookId);

      // Map domain model to response
      DeletePriceBookResponse response =
          DeletePriceBookResponse.newBuilder().setSuccess(true).build();
      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (ProductNotFoundException | InvalidProductDataException e) {
      log.error("Error in deletePriceBook", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    } catch (Exception e) {
      log.error("Unexpected error in deletePriceBook", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }
}
