package com.avantiq.billing.infrastructure.grpc.security;

import com.avantiq.billing.domain.security.model.Role;
import com.avantiq.billing.domain.security.model.UserPrincipal;
import com.avantiq.billing.domain.security.model.UserStatus;
import com.avantiq.billing.infrastructure.security.jwt.JwtTokenProvider;
import io.grpc.Context;
import io.grpc.Contexts;
import io.grpc.Metadata;
import io.grpc.ServerCall;
import io.grpc.ServerCallHandler;
import io.grpc.ServerInterceptor;
import io.grpc.Status;
import java.util.Set;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.devh.boot.grpc.server.interceptor.GrpcGlobalServerInterceptor;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * gRPC Authentication Interceptor that extracts JWT tokens from gRPC metadata and sets up Spring
 * Security context for the request.
 */
@Component
@GrpcGlobalServerInterceptor
@RequiredArgsConstructor
@Slf4j
public class GrpcAuthenticationInterceptor implements ServerInterceptor {

  private static final Metadata.Key<String> AUTHORIZATION_HEADER =
      Metadata.Key.of("authorization", Metadata.ASCII_STRING_MARSHALLER);

  private static final String BEARER_PREFIX = "Bearer ";

  // Context key for tenant information
  public static final Context.Key<Long> TENANT_ID_KEY = Context.key("tenantId");
  public static final Context.Key<UUID> USER_ID_KEY = Context.key("userId");
  public static final Context.Key<Set<UUID>> SEGMENTS_KEY = Context.key("segments");

  private final JwtTokenProvider jwtTokenProvider;

  @Override
  public <ReqT, RespT> ServerCall.Listener<ReqT> interceptCall(
      ServerCall<ReqT, RespT> call, Metadata headers, ServerCallHandler<ReqT, RespT> next) {

    // Skip authentication for authentication service methods and system services
    String methodName = call.getMethodDescriptor().getFullMethodName();
    if (methodName.contains("AuthenticationService/Login")
        || methodName.contains("AuthenticationService/ValidateToken")
        || methodName.contains("grpc.reflection.v1alpha.ServerReflection")
        || methodName.contains("grpc.health.v1.Health")) {
      return next.startCall(call, headers);
    }

    String authHeader = headers.get(AUTHORIZATION_HEADER);

    if (!StringUtils.hasText(authHeader)) {
      log.debug("No authorization header found in gRPC call");
      call.close(
          Status.UNAUTHENTICATED.withDescription("Missing authorization header"), new Metadata());
      return new ServerCall.Listener<>() {};
    }

    if (!authHeader.startsWith(BEARER_PREFIX)) {
      log.debug("Invalid authorization header format in gRPC call");
      call.close(
          Status.UNAUTHENTICATED.withDescription("Invalid authorization header format"),
          new Metadata());
      return new ServerCall.Listener<>() {};
    }

    String jwt = authHeader.substring(BEARER_PREFIX.length());

    if (!jwtTokenProvider.validateToken(jwt)) {
      log.debug("Invalid JWT token in gRPC call");
      call.close(Status.UNAUTHENTICATED.withDescription("Invalid JWT token"), new Metadata());
      return new ServerCall.Listener<>() {};
    }

    try {
      UserPrincipal userPrincipal = createUserPrincipalFromJwt(jwt);

      // Set Spring Security context
      setSecurityContext(userPrincipal);

      // Create gRPC context with tenant information
      Context context =
          Context.current()
              .withValue(TENANT_ID_KEY, userPrincipal.getTenantId())
              .withValue(USER_ID_KEY, userPrincipal.getUserId())
              .withValue(SEGMENTS_KEY, userPrincipal.getAssignedSegments());

      log.debug(
          "Authenticated gRPC call for user: {}, tenant: {}",
          userPrincipal.getUsername(),
          userPrincipal.getTenantId());

      return Contexts.interceptCall(context, call, headers, next);

    } catch (Exception ex) {
      log.error("Error processing JWT token in gRPC call", ex);
      call.close(Status.INTERNAL.withDescription("Authentication error"), new Metadata());
      return new ServerCall.Listener<>() {};
    }
  }

  /** Create UserPrincipal from JWT token. */
  private UserPrincipal createUserPrincipalFromJwt(String jwt) {
    UUID userId = jwtTokenProvider.getUserIdFromToken(jwt);
    String username = jwtTokenProvider.getUsernameFromToken(jwt);
    Long tenantId = jwtTokenProvider.getTenantIdFromToken(jwt);
    Set<Role> roles = jwtTokenProvider.getRolesFromToken(jwt);
    Set<UUID> segments = jwtTokenProvider.getSegmentsFromToken(jwt);

    // Email is not included in JWT for security - it should be fetched from database if needed
    return UserPrincipal.builder()
        .userId(userId)
        .username(username)
        .email(null) // Email removed from JWT for security
        .tenantId(tenantId)
        .roles(roles)
        .assignedSegments(segments)
        .status(UserStatus.ACTIVE)
        .build();
  }

  /** Set Spring Security context for the current thread. */
  private void setSecurityContext(UserPrincipal userPrincipal) {
    UsernamePasswordAuthenticationToken authentication =
        new UsernamePasswordAuthenticationToken(
            userPrincipal, null, userPrincipal.getAuthorities());

    SecurityContextHolder.getContext().setAuthentication(authentication);
  }

  /** Get current tenant ID from gRPC context. */
  public static Long getCurrentTenantId() {
    return TENANT_ID_KEY.get();
  }

  /** Get current user ID from gRPC context. */
  public static UUID getCurrentUserId() {
    return USER_ID_KEY.get();
  }

  /** Get current user's accessible segments from gRPC context. */
  public static Set<UUID> getCurrentUserSegments() {
    return SEGMENTS_KEY.get();
  }
}
