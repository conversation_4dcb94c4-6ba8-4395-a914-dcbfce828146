package com.avantiq.billing.infrastructure.grpc.mapper;

import com.avantiq.billing.domain.product.model.Product;
import com.avantiq.billing.product.grpc.*;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

public class ProductMapper {

  private ProductMapper() {}

  /**
   * Convert a gRPC Product message to a domain Product entity.
   *
   * @param grpcProduct the gRPC Product message
   * @return the domain Product entity
   */
  public static Product toDomain(CreateProductRequest grpcProduct) {
    if (grpcProduct == null) {
      return null;
    }

    Product product = new Product();
    product.setName(grpcProduct.getName());
    product.setDescription(grpcProduct.getDescription());
    product.setPrice(java.math.BigDecimal.valueOf(grpcProduct.getPrice()));

    // Map additional fields
    if (!grpcProduct.getSku().isEmpty()) {
      product.setSku(grpcProduct.getSku());
    }
    if (!grpcProduct.getTaxCode().isEmpty()) {
      product.setTaxCode(grpcProduct.getTaxCode());
    }
    if (!grpcProduct.getGlCode().isEmpty()) {
      product.setGlCode(grpcProduct.getGlCode());
    }
    if (!grpcProduct.getStatus().isEmpty()) {
      product.setStatus(grpcProduct.getStatus());
    }
    if (!grpcProduct.getVisibility().isEmpty()) {
      product.setVisibility(grpcProduct.getVisibility());
    }
    if (!grpcProduct.getProductFamilyId().isEmpty()) {
      product.setProductFamilyId(UUID.fromString(grpcProduct.getProductFamilyId()));
    }

    // Set creation and update timestamps
    product.setCreatedAt(LocalDateTime.now());
    product.setUpdatedAt(LocalDateTime.now());

    return product;
  }

  /**
   * Convert a domain Product entity to a gRPC Product message.
   *
   * @param product the domain Product entity
   * @return the gRPC Product message
   */
  public static ProductResponse toProto(Product product) {
    if (product == null) {
      return null;
    }

    ProductResponse.Builder builder =
        ProductResponse.newBuilder()
            .setId(product.getId() != null ? product.getId().toString() : "")
            .setName(product.getName() != null ? product.getName() : "")
            .setDescription(product.getDescription() != null ? product.getDescription() : "")
            .setPrice(product.getPrice() != null ? product.getPrice().doubleValue() : 0.0);

    // Set optional fields if they exist
    if (product.getSku() != null) {
      builder.setSku(product.getSku());
    }
    if (product.getTaxCode() != null) {
      builder.setTaxCode(product.getTaxCode());
    }
    if (product.getGlCode() != null) {
      builder.setGlCode(product.getGlCode());
    }
    if (product.getStatus() != null) {
      builder.setStatus(product.getStatus());
    }
    if (product.getVisibility() != null) {
      builder.setVisibility(product.getVisibility());
    }
    if (product.getProductFamilyId() != null) {
      builder.setProductFamilyId(product.getProductFamilyId().toString());
    }
    if (product.getCreatedAt() != null) {
      builder.setCreatedAt(product.getCreatedAt().toString());
    }
    if (product.getUpdatedAt() != null) {
      builder.setUpdatedAt(product.getUpdatedAt().toString());
    }

    return builder.build();
  }

  /**
   * Convert a list of domain Product entities to a gRPC ListProductsResponse message.
   *
   * @param products the list of domain Product entities
   * @return the gRPC ListProductsResponse message
   */
  public static ListProductsResponse toGrpcListResponse(List<Product> products) {
    ListProductsResponse.Builder responseBuilder = ListProductsResponse.newBuilder();

    for (Product product : products) {
      responseBuilder.addProducts(toProto(product));
    }

    return responseBuilder.build();
  }
}
