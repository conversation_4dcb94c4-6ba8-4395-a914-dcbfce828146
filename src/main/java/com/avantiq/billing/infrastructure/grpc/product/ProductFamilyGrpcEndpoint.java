package com.avantiq.billing.infrastructure.grpc.product;

import com.avantiq.billing.application.product.ProductFamilyApplicationService;
import com.avantiq.billing.domain.product.exception.InvalidProductDataException;
import com.avantiq.billing.domain.product.exception.ProductNotFoundException;
import com.avantiq.billing.domain.product.model.ProductFamily;
import com.avantiq.billing.infrastructure.grpc.common.GrpcExceptionHandler;
import com.avantiq.billing.infrastructure.grpc.mapper.ProductFamilyMapper;
import com.avantiq.billing.product.grpc.CreateProductFamilyRequest;
import com.avantiq.billing.product.grpc.DeleteProductFamilyRequest;
import com.avantiq.billing.product.grpc.DeleteProductFamilyResponse;
import com.avantiq.billing.product.grpc.GetProductFamilyRequest;
import com.avantiq.billing.product.grpc.ListProductFamiliesRequest;
import com.avantiq.billing.product.grpc.ListProductFamiliesResponse;
import com.avantiq.billing.product.grpc.ProductFamilyResponse;
import com.avantiq.billing.product.grpc.ProductFamilyServiceGrpc;
import com.avantiq.billing.product.grpc.UpdateProductFamilyRequest;
import io.grpc.stub.StreamObserver;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ProductFamilyGrpcEndpoint
    extends ProductFamilyServiceGrpc.ProductFamilyServiceImplBase {

  private final ProductFamilyApplicationService productFamilyApplicationService;

  @Autowired
  public ProductFamilyGrpcEndpoint(
      ProductFamilyApplicationService productFamilyApplicationService) {
    this.productFamilyApplicationService = productFamilyApplicationService;
  }

  @Override
  public void createProductFamily(
      CreateProductFamilyRequest request, StreamObserver<ProductFamilyResponse> responseObserver) {
    try {
      // Map request to domain model
      ProductFamily productFamily = ProductFamilyMapper.toDomain(request);

      // Validate product family data
      if (productFamily.getName() == null || productFamily.getName().trim().isEmpty()) {
        throw new InvalidProductDataException("Product family name is required");
      }

      // Create product family
      ProductFamily createdProductFamily =
          productFamilyApplicationService.createProductFamily(productFamily);

      // Map domain model to response
      ProductFamilyResponse response = ProductFamilyMapper.toProto(createdProductFamily);
      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (InvalidProductDataException e) {
      log.error("Error in createProductFamily", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    } catch (Exception e) {
      log.error("Unexpected error in createProductFamily", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }

  @Override
  public void getProductFamily(
      GetProductFamilyRequest request, StreamObserver<ProductFamilyResponse> responseObserver) {
    try {
      String productFamilyId = request.getId();

      // Validate product family ID
      if (productFamilyId == null || productFamilyId.trim().isEmpty()) {
        throw new InvalidProductDataException("Product family ID is required");
      }

      // Get product family
      Optional<ProductFamily> productFamilyOpt =
          productFamilyApplicationService.getProductFamilyById(productFamilyId);

      if (productFamilyOpt.isEmpty()) {
        throw new ProductNotFoundException(productFamilyId);
      }

      // Map domain model to response
      ProductFamilyResponse response = ProductFamilyMapper.toProto(productFamilyOpt.get());
      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (ProductNotFoundException | InvalidProductDataException e) {
      log.error("Error in getProductFamily", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    } catch (Exception e) {
      log.error("Unexpected error in getProductFamily", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }

  @Override
  public void listProductFamilies(
      ListProductFamiliesRequest request,
      StreamObserver<ListProductFamiliesResponse> responseObserver) {
    try {
      // List all product families
      List<ProductFamily> productFamilies =
          productFamilyApplicationService.listAllProductFamilies();

      // Map domain models to response
      ListProductFamiliesResponse response =
          ProductFamilyMapper.toGrpcListResponse(productFamilies);
      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (Exception e) {
      log.error("Unexpected error in listProductFamilies", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }

  @Override
  public void updateProductFamily(
      UpdateProductFamilyRequest request, StreamObserver<ProductFamilyResponse> responseObserver) {
    try {
      String productFamilyId = request.getId();

      // Validate product family ID
      if (productFamilyId == null || productFamilyId.trim().isEmpty()) {
        throw new InvalidProductDataException("Product family ID is required");
      }

      // Check if product family exists
      Optional<ProductFamily> existingProductFamilyOpt =
          productFamilyApplicationService.getProductFamilyById(productFamilyId);

      if (existingProductFamilyOpt.isEmpty()) {
        throw new ProductNotFoundException(productFamilyId);
      }

      // Map request to domain model
      ProductFamily productFamily = ProductFamilyMapper.toDomain(request);

      // Validate product family data
      if (productFamily.getName() == null || productFamily.getName().trim().isEmpty()) {
        throw new InvalidProductDataException("Product family name is required");
      }

      // Preserve creation date
      productFamily.setCreatedAt(existingProductFamilyOpt.get().getCreatedAt());

      // Update product family
      ProductFamily updatedProductFamily =
          productFamilyApplicationService.updateProductFamily(productFamily);

      // Map domain model to response
      ProductFamilyResponse response = ProductFamilyMapper.toProto(updatedProductFamily);
      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (ProductNotFoundException | InvalidProductDataException e) {
      log.error("Error in updateProductFamily", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    } catch (Exception e) {
      log.error("Unexpected error in updateProductFamily", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }

  @Override
  public void deleteProductFamily(
      DeleteProductFamilyRequest request,
      StreamObserver<DeleteProductFamilyResponse> responseObserver) {
    try {
      String productFamilyId = request.getId();

      // Validate product family ID
      if (productFamilyId == null || productFamilyId.trim().isEmpty()) {
        throw new InvalidProductDataException("Product family ID is required");
      }

      // Check if product family exists
      Optional<ProductFamily> existingProductFamilyOpt =
          productFamilyApplicationService.getProductFamilyById(productFamilyId);

      if (existingProductFamilyOpt.isEmpty()) {
        throw new ProductNotFoundException(productFamilyId);
      }

      // Delete product family
      productFamilyApplicationService.deleteProductFamily(productFamilyId);

      // Map domain model to response
      DeleteProductFamilyResponse response =
          DeleteProductFamilyResponse.newBuilder().setSuccess(true).build();
      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (ProductNotFoundException | InvalidProductDataException e) {
      log.error("Error in deleteProductFamily", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    } catch (Exception e) {
      log.error("Unexpected error in deleteProductFamily", e);
      responseObserver.onError(GrpcExceptionHandler.handleExceptionWithMetadata(e));
    }
  }
}
