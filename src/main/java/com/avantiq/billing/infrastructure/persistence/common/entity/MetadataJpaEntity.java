package com.avantiq.billing.infrastructure.persistence.common.entity;

import jakarta.persistence.*;
import java.util.UUID;

/** JPA entity for Metadata. */
@Entity
@Table(name = "metadata")
public class MetadataJpaEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.AUTO)
  private UUID id;

  @Column(name = "entity_id", nullable = false)
  private UUID entityId;

  @Column(name = "entity_type", nullable = false)
  private String entityType; // Discriminator column for Product, Bundle, Price

  @Column(nullable = false)
  private String key;

  @Column(nullable = false)
  private String value;

  private Long tenantId;

  // Getters and setters
  public UUID getId() {
    return id;
  }

  public void setId(UUID id) {
    this.id = id;
  }

  public UUID getEntityId() {
    return entityId;
  }

  public void setEntityId(UUID entityId) {
    this.entityId = entityId;
  }

  public String getEntityType() {
    return entityType;
  }

  public void setEntityType(String entityType) {
    this.entityType = entityType;
  }

  public String getKey() {
    return key;
  }

  public void setKey(String key) {
    this.key = key;
  }

  public String getValue() {
    return value;
  }

  public void setValue(String value) {
    this.value = value;
  }

  public Long getTenantId() {
    return tenantId;
  }

  public void setTenantId(Long tenantId) {
    this.tenantId = tenantId;
  }
}
