package com.avantiq.billing.infrastructure.persistence.product.entity;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.UUID;

/** JPA entity for Bundle. */
@Entity
@Table(name = "bundles")
public class BundleJpaEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.AUTO)
  private UUID bundleId;

  private String name;

  private String description;

  private String status;

  private String pricingStrategy;

  private long version;

  private LocalDateTime createdAt;

  private LocalDateTime updatedAt;

  private Long tenantId;

  // Getters and setters
  public UUID getBundleId() {
    return bundleId;
  }

  public void setBundleId(UUID bundleId) {
    this.bundleId = bundleId;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public String getPricingStrategy() {
    return pricingStrategy;
  }

  public void setPricingStrategy(String pricingStrategy) {
    this.pricingStrategy = pricingStrategy;
  }

  public long getVersion() {
    return version;
  }

  public void setVersion(long version) {
    this.version = version;
  }

  public LocalDateTime getCreatedAt() {
    return createdAt;
  }

  public void setCreatedAt(LocalDateTime createdAt) {
    this.createdAt = createdAt;
  }

  public LocalDateTime getUpdatedAt() {
    return updatedAt;
  }

  public void setUpdatedAt(LocalDateTime updatedAt) {
    this.updatedAt = updatedAt;
  }

  public Long getTenantId() {
    return tenantId;
  }

  public void setTenantId(Long tenantId) {
    this.tenantId = tenantId;
  }
}
