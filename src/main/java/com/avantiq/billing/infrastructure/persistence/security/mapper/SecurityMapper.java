package com.avantiq.billing.infrastructure.persistence.security.mapper;

import com.avantiq.billing.domain.common.Metadata;
import com.avantiq.billing.domain.security.model.*;
import com.avantiq.billing.infrastructure.persistence.common.mapper.MetadataMapper;
import com.avantiq.billing.infrastructure.persistence.security.entity.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

/**
 * Mapper for converting between security domain models and JPA entities. Handles User, Role, and
 * Permission mappings with proper tenant isolation.
 */
@Component
public class SecurityMapper {

  private final MetadataMapper metadataMapper;

  public SecurityMapper(MetadataMapper metadataMapper) {
    this.metadataMapper = metadataMapper;
  }

  /** Convert UserJpaEntity to User domain model. */
  public User toDomain(UserJpaEntity entity) {
    if (entity == null) {
      return null;
    }

    Set<Role> roles =
        entity.getUserRoles() != null
            ? entity.getUserRoles().stream()
                .map(ur -> mapRoleEntityToRole(ur.getRole().getName()))
                .collect(Collectors.toSet())
            : Set.of();

    Set<java.util.UUID> segments =
        entity.getUserSegments() != null
            ? entity.getUserSegments().stream()
                .map(UserSegmentJpaEntity::getSegmentId)
                .collect(Collectors.toSet())
            : Set.of();

    Metadata metadata =
        entity.getMetadata() != null ? metadataMapper.toDomain(entity.getMetadata()) : null;

    User user =
        User.builder()
            .userId(entity.getUserId())
            .username(entity.getUsername())
            .email(entity.getEmail())
            .passwordHash(entity.getPasswordHash())
            .tenantId(entity.getTenantId())
            .roles(roles)
            .assignedSegments(segments)
            .status(mapUserStatusEntityToUserStatus(entity.getStatus()))
            .metadata(metadata)
            .build();

    // Validate user data integrity during mapping
    User.validateUser(user);
    return user;
  }

  /** Convert User domain model to UserJpaEntity. */
  public UserJpaEntity toEntity(User domain) {
    if (domain == null) {
      return null;
    }

    var entity =
        UserJpaEntity.builder()
            .userId(domain.getUserId())
            .username(domain.getUsername())
            .email(domain.getEmail())
            .passwordHash(domain.getPasswordHash())
            .tenantId(domain.getTenantId())
            .status(mapUserStatusToUserStatusEntity(domain.getStatus()))
            .metadata(
                domain.getMetadata() != null
                    ? metadataMapper.toEntity(domain.getMetadata(), "USER")
                    : null)
            .build();

    return entity;
  }

  /** Update UserJpaEntity with User domain model data. */
  public void updateEntity(UserJpaEntity entity, User domain) {
    if (entity == null || domain == null) {
      return;
    }

    entity.setUsername(domain.getUsername());
    entity.setEmail(domain.getEmail());
    entity.setPasswordHash(domain.getPasswordHash());
    entity.setStatus(mapUserStatusToUserStatusEntity(domain.getStatus()));

    if (domain.getMetadata() != null) {
      entity.setMetadata(metadataMapper.toEntity(domain.getMetadata(), "USER"));
    }
  }

  /** Convert RoleJpaEntity to Role domain model. */
  public Role mapRoleEntityToRole(RoleJpaEntity.RoleTypeEntity roleEntity) {
    return switch (roleEntity) {
      case TENANT_ADMIN -> Role.TENANT_ADMIN;
      case SEGMENT_ADMIN -> Role.SEGMENT_ADMIN;
      case USER -> Role.USER;
      case READONLY -> Role.READONLY;
    };
  }

  /** Convert Role domain model to RoleJpaEntity. */
  public RoleJpaEntity.RoleTypeEntity mapRoleToRoleEntity(Role role) {
    return switch (role) {
      case TENANT_ADMIN -> RoleJpaEntity.RoleTypeEntity.TENANT_ADMIN;
      case SEGMENT_ADMIN -> RoleJpaEntity.RoleTypeEntity.SEGMENT_ADMIN;
      case USER -> RoleJpaEntity.RoleTypeEntity.USER;
      case READONLY -> RoleJpaEntity.RoleTypeEntity.READONLY;
    };
  }

  /** Convert PermissionJpaEntity to Permission domain model. */
  public Permission mapPermissionEntityToPermission(
      PermissionJpaEntity.PermissionTypeEntity permissionEntity) {
    return switch (permissionEntity) {
      case READ_ALL_TENANT_DATA -> Permission.READ_ALL_TENANT_DATA;
      case WRITE_ALL_TENANT_DATA -> Permission.WRITE_ALL_TENANT_DATA;
      case READ_SEGMENT_DATA -> Permission.READ_SEGMENT_DATA;
      case WRITE_SEGMENT_DATA -> Permission.WRITE_SEGMENT_DATA;
      case MANAGE_USERS -> Permission.MANAGE_USERS;
      case MANAGE_SEGMENT_USERS -> Permission.MANAGE_SEGMENT_USERS;
      case MANAGE_SEGMENTS -> Permission.MANAGE_SEGMENTS;
    };
  }

  /** Convert Permission domain model to PermissionJpaEntity. */
  public PermissionJpaEntity.PermissionTypeEntity mapPermissionToPermissionEntity(
      Permission permission) {
    return switch (permission) {
      case READ_ALL_TENANT_DATA -> PermissionJpaEntity.PermissionTypeEntity.READ_ALL_TENANT_DATA;
      case WRITE_ALL_TENANT_DATA -> PermissionJpaEntity.PermissionTypeEntity.WRITE_ALL_TENANT_DATA;
      case READ_SEGMENT_DATA -> PermissionJpaEntity.PermissionTypeEntity.READ_SEGMENT_DATA;
      case WRITE_SEGMENT_DATA -> PermissionJpaEntity.PermissionTypeEntity.WRITE_SEGMENT_DATA;
      case MANAGE_USERS -> PermissionJpaEntity.PermissionTypeEntity.MANAGE_USERS;
      case MANAGE_SEGMENT_USERS -> PermissionJpaEntity.PermissionTypeEntity.MANAGE_SEGMENT_USERS;
      case MANAGE_SEGMENTS -> PermissionJpaEntity.PermissionTypeEntity.MANAGE_SEGMENTS;
    };
  }

  /** Convert UserStatusEntity to UserStatus domain model. */
  private UserStatus mapUserStatusEntityToUserStatus(UserJpaEntity.UserStatusEntity statusEntity) {
    return switch (statusEntity) {
      case ACTIVE -> UserStatus.ACTIVE;
      case INACTIVE -> UserStatus.INACTIVE;
      case SUSPENDED -> UserStatus.SUSPENDED;
      case PENDING_ACTIVATION -> UserStatus.PENDING_ACTIVATION;
    };
  }

  /** Convert UserStatus domain model to UserStatusEntity. */
  private UserJpaEntity.UserStatusEntity mapUserStatusToUserStatusEntity(UserStatus status) {
    return switch (status) {
      case ACTIVE -> UserJpaEntity.UserStatusEntity.ACTIVE;
      case INACTIVE -> UserJpaEntity.UserStatusEntity.INACTIVE;
      case SUSPENDED -> UserJpaEntity.UserStatusEntity.SUSPENDED;
      case PENDING_ACTIVATION -> UserJpaEntity.UserStatusEntity.PENDING_ACTIVATION;
    };
  }

  /** Create UserRoleJpaEntity entities from user roles. */
  public List<UserRoleJpaEntity> createUserRoleEntities(UserJpaEntity userEntity, Set<Role> roles) {
    if (roles == null || roles.isEmpty()) {
      return new ArrayList<>();
    }

    return roles.stream()
        .map(
            role ->
                UserRoleJpaEntity.builder().user(userEntity).role(createRoleEntity(role)).build())
        .collect(Collectors.toList());
  }

  /** Create UserSegmentJpaEntity entities from segment IDs. */
  public List<UserSegmentJpaEntity> createUserSegmentEntities(
      UserJpaEntity userEntity, Set<java.util.UUID> segmentIds, Long tenantId) {
    if (segmentIds == null || segmentIds.isEmpty()) {
      return new ArrayList<>();
    }

    return segmentIds.stream()
        .map(
            segmentId ->
                UserSegmentJpaEntity.builder()
                    .user(userEntity)
                    .segmentId(segmentId)
                    .tenantId(tenantId)
                    .build())
        .collect(Collectors.toList());
  }

  /** Create RoleJpaEntity from Role domain model. */
  private RoleJpaEntity createRoleEntity(Role role) {
    return RoleJpaEntity.builder().name(mapRoleToRoleEntity(role)).build();
  }
}
