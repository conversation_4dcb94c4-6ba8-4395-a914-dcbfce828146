package com.avantiq.billing.infrastructure.persistence.customer.entity;

import jakarta.persistence.*;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;

/**
 * JPA entity for Address. This is the persistence model for addresses, separate from the domain
 * model.
 */
@Setter
@Getter
@Entity
@Table(name = "address")
public class AddressJpaEntity {
  public enum AddressType {
    BILLING,
    SHIPPING
  }

  // Getters and setters
  @Id
  @GeneratedValue(strategy = GenerationType.AUTO)
  private UUID id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "customer_id", nullable = false)
  private CustomerJpaEntity customer;

  @Column(nullable = false)
  private String street;

  @Column(nullable = false)
  private String city;

  @Column(nullable = false)
  private String state;

  @Column(nullable = false)
  private String postalCode;

  @Column(nullable = false)
  private String country;

  @Enumerated(EnumType.STRING)
  @Column(nullable = false)
  private AddressType addressType;
}
