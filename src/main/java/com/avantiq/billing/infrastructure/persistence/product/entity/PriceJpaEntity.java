package com.avantiq.billing.infrastructure.persistence.product.entity;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.UUID;

/** JPA entity for Price. */
@Entity
@Table(name = "prices")
public class PriceJpaEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.AUTO)
  private UUID id;

  @Column(name = "product_id")
  private UUID productId;

  @Column(name = "price_book_id")
  private UUID priceBookId;

  @Enumerated(EnumType.STRING)
  @Column(name = "billing_frequency", nullable = false)
  private BillingFrequency billingFrequency;

  private String currency;

  @Column(name = "unit_of_measure")
  private String unitOfMeasure;

  @Enumerated(EnumType.STRING)
  @Column(name = "price_type", nullable = false)
  private PriceType priceType;

  @Enumerated(EnumType.STRING)
  @Column(name = "price_strategy", nullable = false)
  private PriceStrategy priceStrategy;

  @Enumerated(EnumType.STRING)
  @Column(name = "billing_strategy", nullable = false)
  private BillingStrategy billingStrategy;

  @Column(name = "pricing", columnDefinition = "JSONB")
  private String pricing;

  @Enumerated(EnumType.STRING)
  @Column(name = "proration_policy", nullable = false)
  private ProrationPolicy prorationPolicy;

  @Column(name = "is_default")
  private boolean isDefault;

  @Column(name = "is_grandfathered")
  private boolean isGrandfathered;

  private long version;

  private LocalDateTime createdAt;

  private LocalDateTime updatedAt;

  private Long tenantId;

  // Getters and setters
  public UUID getId() {
    return id;
  }

  public void setId(UUID id) {
    this.id = id;
  }

  public UUID getProductId() {
    return productId;
  }

  public void setProductId(UUID productId) {
    this.productId = productId;
  }

  public UUID getPriceBookId() {
    return priceBookId;
  }

  public void setPriceBookId(UUID priceBookId) {
    this.priceBookId = priceBookId;
  }

  public BillingFrequency getBillingFrequency() {
    return billingFrequency;
  }

  public void setBillingFrequency(BillingFrequency billingFrequency) {
    this.billingFrequency = billingFrequency;
  }

  public String getCurrency() {
    return currency;
  }

  public void setCurrency(String currency) {
    this.currency = currency;
  }

  public String getUnitOfMeasure() {
    return unitOfMeasure;
  }

  public void setUnitOfMeasure(String unitOfMeasure) {
    this.unitOfMeasure = unitOfMeasure;
  }

  public BillingStrategy getBillingStrategy() {
    return billingStrategy;
  }

  public void setBillingStrategy(BillingStrategy billingStrategy) {
    this.billingStrategy = billingStrategy;
  }

  public ProrationPolicy getProrationPolicy() {
    return prorationPolicy;
  }

  public void setProrationPolicy(ProrationPolicy prorationPolicy) {
    this.prorationPolicy = prorationPolicy;
  }

  public boolean isDefault() {
    return isDefault;
  }

  public void setDefault(boolean isDefault) {
    this.isDefault = isDefault;
  }

  public boolean isGrandfathered() {
    return isGrandfathered;
  }

  public void setGrandfathered(boolean isGrandfathered) {
    this.isGrandfathered = isGrandfathered;
  }

  public long getVersion() {
    return version;
  }

  public void setVersion(long version) {
    this.version = version;
  }

  public LocalDateTime getCreatedAt() {
    return createdAt;
  }

  public void setCreatedAt(LocalDateTime createdAt) {
    this.createdAt = createdAt;
  }

  public LocalDateTime getUpdatedAt() {
    return updatedAt;
  }

  public void setUpdatedAt(LocalDateTime updatedAt) {
    this.updatedAt = updatedAt;
  }

  public Long getTenantId() {
    return tenantId;
  }

  public void setTenantId(Long tenantId) {
    this.tenantId = tenantId;
  }

  public PriceType getPriceType() {
    return priceType;
  }

  public void setPriceType(PriceType priceType) {
    this.priceType = priceType;
  }

  public PriceStrategy getPriceStrategy() {
    return priceStrategy;
  }

  public void setPriceStrategy(PriceStrategy priceStrategy) {
    this.priceStrategy = priceStrategy;
  }

  public String getPricing() {
    return pricing;
  }

  public void setPricing(String pricing) {
    this.pricing = pricing;
  }

  public enum BillingFrequency {
    MONTHLY,
    SEMI_ANNUALLY,
    ANNUALLY,
    ON_EVENT
  }

  public enum PriceType {
    RECURRING,
    USAGE,
    ONE_TIME,
    COMMITMENT
  }

  public enum PriceStrategy {
    FLAT,
    TIERED,
    VOLUME,
    OVERAGE,
    RAMP,
    PER_UNIT
  }

  public enum BillingStrategy {
    PREPAID,
    POSTPAID
  }

  public enum ProrationPolicy {
    FULL,
    PARTIAL,
    NONE
  }
}
