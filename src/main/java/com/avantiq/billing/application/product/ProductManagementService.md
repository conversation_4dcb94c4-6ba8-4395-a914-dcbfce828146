# Product Management Service

## Functional Requirements

### 1. Product Catalog Structure & Governance
- **FR1.1**: The system shall support a hierarchical catalog structure with products grouped into product families.
- **FR1.2**: The system shall support managing multiple catalogs segmented by market, legal entity, or geography.
- **FR1.3**: The system shall support activation/deactivation of products and price books by version and date.
- **FR1.4**: The system shall enforce uniqueness of product identifiers across the catalog within a legal/segment context.
- **FR1.5**: The system shall allow tagging, categorization, and searching/filtering of products.

### 2. Product & Product Family Management
- **FR2.1**: The system shall allow creation of product families to logically group products (e.g., "Storage Plans", "Data APIs").
- **FR2.2**: Each product shall be associated with one or more product families.
- **FR2.3**: Products shall have metadata including name, description, SKU, display name, internal/external visibility flags.
- **FR2.4**: Products may be associated with one or more price models across different markets or entities.
- **FR2.5**: Products may be cloned to accelerate catalog creation across regions/entities.

### 3. Bundling & Packaging
- **FR3.1**: The system shall support definition of bundles made up of multiple standalone products.
- **FR3.2**: Bundles may include nested components (sub-bundles or options).
- **FR3.3**: Bundles may support rules for optional vs required components.
- **FR3.4**: Bundles may define bundle-level pricing or sum-of-parts pricing.
- **FR3.5**: Bundles must be represented in downstream subscription/service contracts.

### 4. Price Book Management
- **FR4.1**: The system shall support creation of price books scoped to:
  - Customer segments
  - Legal entities
  - Currencies/markets
  - Channels or sales models
- **FR4.2**: Price books shall contain one or more price models per product.
- **FR4.3**: Price books may be time-bound with effective/expiration dates.
- **FR4.4**: The system shall allow price books to be versioned and superseded.

### 5. Price Models & Charge Strategies
- **FR5.1**: Each product shall support multiple charge types:
  - Recurring (monthly, annual, etc.)
  - Usage-based (tiered, volume, overage, pooled)
  - One-time (setup, activation)
  - Prepaid commitment (contract minimums)
  - Arrears commitment (postpaid reconciliation)
- **FR5.2**: Each price model shall define how charges are calculated (flat, tiered, per-unit, percent, etc.).
- **FR5.3**: Each price model shall support proration logic for mid-cycle changes.
- **FR5.4**: A product may have multiple price models for different price books or customer segments.

### 6. Prices (Rate Cards & Customer Charging)
- **FR6.1**: Prices define the customer-facing rates that drive billing calculations.
- **FR6.2**: Prices shall support:
  - Multi-currency support
  - Tiered and volume pricing
  - UOM definitions (e.g., GB, API Call)
- **FR6.3**: Prices may be overridden at contract/subscription level (if allowed by config).
- **FR6.4**: Changes to prices shall support either:
  - Global update (apply to new and existing customers)
  - Grandfathering (new prices only apply to new purchases)
- **FR6.5**: Prices may inherit tax settings or discounts based on configuration.

### 7. Versioning & Change Management
- **FR7.1**: Products, bundles, and price models shall be versioned.
- **FR7.2**: Each version shall include effective start and end dates.
- **FR7.3**: Changes to product structure or pricing shall not impact existing subscriptions unless explicitly opted in.
- **FR7.4**: The system shall support publishing, draft, and deprecated states for catalog items.

### 8. Integration & Extensibility
- **FR8.1**: The system shall expose APIs for managing products, price books, price models, and bundles.
- **FR8.2**: Each catalog item shall support external references for sync with CPQ/CRM systems (e.g., Salesforce Product2 ID).
- **FR8.3**: Product metadata shall be extensible via custom fields and attributes.
- **FR8.4**: The system shall support catalog synchronization across regions via jobs or event-based sync.

### 9. Reporting & Audit
- **FR9.1**: All changes to product/pricing data shall be audit-logged with timestamp and user info.
- **FR9.2**: The system shall provide visibility into active products and pricing history over time.
- **FR9.3**: The system shall support exporting catalog data for analysis and compliance.

## Database Schema Definitions

### Product
```sql
CREATE TABLE products (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10, 2),
    product_family_id UUID,
    sku VARCHAR(100),
    tax_code VARCHAR(50),
    gl_code VARCHAR(50),
    status VARCHAR(50),
    visibility VARCHAR(50),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    tenant_id BIGINT
);
```

### Product Family
```sql
CREATE TABLE product_families (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    tenant_id BIGINT
);
```

### Bundle
```sql
CREATE TABLE bundles (
    bundle_id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(50),
    pricing_strategy VARCHAR(50),
    version BIGINT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    tenant_id BIGINT
);
```

### Price
```sql
CREATE TABLE prices (
    id UUID PRIMARY KEY,
    product_id UUID,
    price_book_id UUID,
    billing_frequency VARCHAR(50) NOT NULL,
    currency VARCHAR(10),
    unit_of_measure VARCHAR(50),
    charge_type VARCHAR(50) NOT NULL,
    charge_strategy VARCHAR(50) NOT NULL,
    billing_strategy VARCHAR(50) NOT NULL,
    charge JSONB,
    proration_policy VARCHAR(50) NOT NULL,
    is_default BOOLEAN,
    is_grandfathered BOOLEAN,
    version BIGINT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    tenant_id BIGINT
);
```

### Metadata
```sql
CREATE TABLE metadata (
    id UUID PRIMARY KEY,
    entity_id UUID NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    key VARCHAR(255) NOT NULL,
    value TEXT NOT NULL,
    tenant_id BIGINT
);
```
