spring.application.name=avantiq
spring.datasource.url=*************************************************=${DB_SSL_MODE:prefer}
spring.datasource.username=${DB_USERNAME:postgres}
spring.datasource.password=${DB_PASSWORD:}
spring.jpa.hibernate.ddl-auto=update
hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

# JWT Configuration
# IMPORTANT: JWT_SECRET environment variable is REQUIRED
jwt.secret=${JWT_SECRET}
jwt.expiration=${JWT_EXPIRATION:86400000}

# Security Configuration
logging.level.org.springframework.security=INFO
management.endpoints.web.exposure.include=health,info

# Rate Limiting Configuration
security.rate-limit.max-requests=${RATE_LIMIT_MAX_REQUESTS:100}
security.rate-limit.window-seconds=${RATE_LIMIT_WINDOW_SECONDS:60}

# gRPC Configuration
grpc.server.port=${GRPC_PORT:9090}
grpc.server.reflection-service-enabled=true

# CORS Configuration
cors.allowed.origins=${CORS_ALLOWED_ORIGINS:http://localhost:3000}
cors.allowed.methods=GET,POST,PUT,DELETE,OPTIONS
cors.allowed.headers=*
cors.exposed.headers=Authorization,Content-Type
cors.max.age=3600
