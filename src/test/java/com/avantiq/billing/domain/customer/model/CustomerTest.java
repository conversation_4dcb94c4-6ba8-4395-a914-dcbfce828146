package com.avantiq.billing.domain.customer.model;

import static org.junit.jupiter.api.Assertions.*;

import java.time.LocalDateTime;
import java.util.UUID;
import org.junit.jupiter.api.Test;

class CustomerTest {

  /**
   * Tests for the updateDetails method in the Customer class. The updateDetails method updates the
   * Customer's details including firstName, lastName, email, companyName, vatNumber, and country,
   * and refreshes the updatedAt timestamp.
   */
  @Test
  void testUpdateDetails_updatesAllFields() {
    // Arrange
    UUID id = UUID.randomUUID();
    Customer customer =
        new Customer(
            id,
            "<PERSON>",
            "Doe",
            "<EMAIL>",
            "US",
            UUID.randomUUID(),
            Customer.Status.ACTIVE,
            1L);

    String newFirstName = "Jane";
    String newLastName = "Smith";
    String newEmail = "<EMAIL>";
    String newCompanyName = "TechCorp";
    String newVatNumber = "US123456789";
    String newCountry = "UK";

    LocalDateTime previousUpdatedAt = customer.getUpdatedAt();

    // Act
    customer.updateDetails(
        newFirstName, newLastName, newEmail, newCompanyName, newVatNumber, newCountry);

    // Assert
    assertEquals(newFirstName, customer.getFirstName());
    assertEquals(newLastName, customer.getLastName());
    assertEquals(newEmail, customer.getEmail());
    assertEquals(newCompanyName, customer.getCompanyName());
    assertEquals(newVatNumber, customer.getVatNumber());
    assertEquals(newCountry, customer.getCountry());
    assertNotNull(customer.getUpdatedAt());
    assertTrue(customer.getUpdatedAt().isAfter(previousUpdatedAt));
  }

  @Test
  void testUpdateDetails_doesNotAffectIdOrStatus() {
    // Arrange
    UUID id = UUID.randomUUID();
    Customer.Status initialStatus = Customer.Status.ACTIVE;
    Customer customer =
        new Customer(
            id, "John", "Doe", "<EMAIL>", "US", UUID.randomUUID(), initialStatus, 1L);

    // Act
    customer.updateDetails(
        "Jane", "Smith", "<EMAIL>", "TechCorp", "US123456789", "UK");

    // Assert
    assertEquals(id, customer.getId());
    assertEquals(initialStatus, customer.getStatus());
  }

  @Test
  void testUpdateDetails_setsUpdatedAtToCurrentTime() {
    // Arrange
    Customer customer =
        new Customer(
            UUID.randomUUID(),
            "John",
            "Doe",
            "<EMAIL>",
            "US",
            UUID.randomUUID(),
            Customer.Status.ACTIVE,
            1L);
    LocalDateTime beforeUpdateTime = LocalDateTime.now();

    // Act
    customer.updateDetails(
        "Jane", "Smith", "<EMAIL>", "TechCorp", "US123456789", "UK");

    // Assert
    assertNotNull(customer.getUpdatedAt());
    assertTrue(customer.getUpdatedAt().isAfter(beforeUpdateTime));
  }

  @Test
  void testRemoveAddress_removesAddressSuccessfully() {
    // Arrange
    Customer customer = new Customer();
    customer.setTenantId(1L);
    Address address = new Address();
    customer.addAddress(address);

    // Act
    customer.removeAddress(address);

    // Assert
    assertFalse(customer.getAddresses().contains(address));
  }

  @Test
  void testRemoveAddress_doesNotRemoveNonExistingAddress() {
    // Arrange
    Customer customer = new Customer();
    customer.setTenantId(1L);
    Address address1 = new Address();
    Address address2 = new Address();
    customer.addAddress(address1);

    // Act
    customer.removeAddress(address2);

    // Assert
    assertEquals(1, customer.getAddresses().size());
    assertTrue(customer.getAddresses().contains(address1));
  }

  @Test
  void testRemoveAddress_emptiesAddressListWhenLastAddressRemoved() {
    // Arrange
    Customer customer = new Customer();
    customer.setTenantId(1L);
    Address address = new Address();
    customer.addAddress(address);

    // Act
    customer.removeAddress(address);

    // Assert
    assertTrue(customer.getAddresses().isEmpty());
  }
}
