package com.avantiq.billing.domain.customer.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.avantiq.billing.domain.customer.model.Customer;
import com.avantiq.billing.domain.customer.repository.CustomerRepository;
import com.avantiq.billing.domain.customer.service.impl.CustomerServiceImpl;
import com.avantiq.billing.domain.customer.service.interfaces.CustomerService;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Test to verify CustomerService refactoring works correctly without Spring dependencies. This test
 * proves that the domain service can be instantiated and used without Spring framework.
 */
@ExtendWith(MockitoExtension.class)
class CustomerServiceRefactoringTest {

  @Mock private CustomerRepository customerRepository;

  private CustomerService customerService;

  @BeforeEach
  void setUp() {
    // Create CustomerService without Spring - this proves our refactoring works
    customerService = new CustomerServiceImpl(customerRepository);
  }

  @Test
  void testCustomerServiceCanBeCreatedWithoutSpring() {
    // Test that we can create the service without Spring annotations
    assertNotNull(customerService);
    assertInstanceOf(CustomerServiceImpl.class, customerService);
  }

  @Test
  void testFindByIdWorksWithoutSpring() {
    // Given
    UUID customerId = UUID.randomUUID();
    Customer mockCustomer = new Customer();
    mockCustomer.setId(customerId);
    mockCustomer.setEmail("<EMAIL>");
    mockCustomer.setTenantId(1L);

    when(customerRepository.findById(customerId)).thenReturn(Optional.of(mockCustomer));

    // When
    Optional<Customer> result = customerService.findById(customerId);

    // Then
    assertTrue(result.isPresent());
    assertEquals(customerId, result.get().getId());
    assertEquals("<EMAIL>", result.get().getEmail());
    verify(customerRepository).findById(customerId);
  }

  @Test
  void testExistsByEmailWorksWithoutSpring() {
    // Given
    String email = "<EMAIL>";
    Customer mockCustomer = new Customer();
    mockCustomer.setEmail(email);
    mockCustomer.setTenantId(1L);

    when(customerRepository.findByEmail(email)).thenReturn(Optional.of(mockCustomer));

    // When
    boolean exists = customerService.existsByEmail(email);

    // Then
    assertTrue(exists);
    verify(customerRepository).findByEmail(email);
  }

  @Test
  void testSaveWorksWithoutSpring() {
    // Given
    Customer customer = new Customer();
    customer.setEmail("<EMAIL>");
    customer.setFirstName("John");
    customer.setLastName("Doe");
    customer.setTenantId(1L);

    when(customerRepository.save(any(Customer.class))).thenReturn(customer);

    // When
    Customer savedCustomer = customerService.save(customer);

    // Then
    assertNotNull(savedCustomer);
    assertEquals("<EMAIL>", savedCustomer.getEmail());
    assertEquals("John", savedCustomer.getFirstName());
    assertEquals("Doe", savedCustomer.getLastName());
    verify(customerRepository).save(customer);
  }
}
