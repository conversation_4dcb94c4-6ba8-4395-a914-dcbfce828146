package com.avantiq.billing.infrastructure.security;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.avantiq.billing.domain.security.model.Role;
import com.avantiq.billing.domain.security.model.UserPrincipal;
import com.avantiq.billing.domain.security.model.UserStatus;
import com.avantiq.billing.infrastructure.security.jwt.JwtTokenProvider;
import io.jsonwebtoken.ExpiredJwtException;
import java.time.Instant;
import java.util.Date;
import java.util.Set;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * Integration tests for JWT security flow. Tests token generation, validation, and claim
 * extraction.
 */
class JwtSecurityIntegrationTest {

  private JwtTokenProvider jwtTokenProvider;
  private static final String TEST_SECRET = "ThisIsATestSecretKeyThatIsAtLeast32CharactersLong";
  private static final long TEST_EXPIRATION = 3600000; // 1 hour

  @BeforeEach
  void setUp() {
    jwtTokenProvider = new JwtTokenProvider();
    // Use reflection to set private fields for testing
    ReflectionTestUtils.setField(jwtTokenProvider, "jwtSecret", TEST_SECRET);
    ReflectionTestUtils.setField(jwtTokenProvider, "jwtExpiration", TEST_EXPIRATION);
  }

  @Test
  void testCompleteJwtFlow() {
    // Given - Create a user principal
    UUID userId = UUID.randomUUID();
    String username = "<EMAIL>";
    Long tenantId = 1L;
    Set<Role> roles = Set.of(Role.USER, Role.SEGMENT_ADMIN);
    Set<UUID> segments = Set.of(UUID.randomUUID(), UUID.randomUUID());

    UserPrincipal userPrincipal =
        UserPrincipal.builder()
            .userId(userId)
            .username(username)
            .email(username)
            .tenantId(tenantId)
            .roles(roles)
            .assignedSegments(segments)
            .status(UserStatus.ACTIVE)
            .build();

    // When - Generate token
    String token = jwtTokenProvider.generateToken(userPrincipal);

    // Then - Validate token
    assertThat(token).isNotNull();
    assertThat(jwtTokenProvider.validateToken(token)).isTrue();

    // Verify claims extraction
    assertThat(jwtTokenProvider.getUserIdFromToken(token)).isEqualTo(userId);
    assertThat(jwtTokenProvider.getUsernameFromToken(token)).isEqualTo(username);
    assertThat(jwtTokenProvider.getTenantIdFromToken(token)).isEqualTo(tenantId);

    Set<Role> extractedRoles = jwtTokenProvider.getRolesFromToken(token);
    assertThat(extractedRoles).containsExactlyInAnyOrderElementsOf(roles);

    Set<UUID> extractedSegments = jwtTokenProvider.getSegmentsFromToken(token);
    assertThat(extractedSegments).containsExactlyInAnyOrderElementsOf(segments);

    // Verify expiration
    Date expiration = jwtTokenProvider.getExpirationDateFromToken(token);
    assertThat(expiration).isAfter(new Date());
    assertThat(expiration).isBefore(new Date(System.currentTimeMillis() + TEST_EXPIRATION + 1000));
  }

  @Test
  void testInvalidTokenValidation() {
    // Given
    String invalidToken = "invalid.jwt.token";

    // When/Then
    assertThat(jwtTokenProvider.validateToken(invalidToken)).isFalse();
  }

  @Test
  void testExpiredTokenValidation() {
    // Given - Create an expired token
    JwtTokenProvider expiredTokenProvider = new JwtTokenProvider();
    ReflectionTestUtils.setField(expiredTokenProvider, "jwtSecret", TEST_SECRET);
    ReflectionTestUtils.setField(expiredTokenProvider, "jwtExpiration", -1000L); // Already expired

    UserPrincipal userPrincipal =
        UserPrincipal.builder()
            .userId(UUID.randomUUID())
            .username("<EMAIL>")
            .email("<EMAIL>")
            .tenantId(1L)
            .roles(Set.of(Role.USER))
            .status(UserStatus.ACTIVE)
            .build();

    String expiredToken = expiredTokenProvider.generateToken(userPrincipal);

    // When/Then
    assertThat(jwtTokenProvider.validateToken(expiredToken)).isFalse();
    assertThatThrownBy(() -> jwtTokenProvider.getUserIdFromToken(expiredToken))
        .isInstanceOf(ExpiredJwtException.class);
  }

  @Test
  void testTokenWithDifferentSecret() {
    // Given - Create token with different secret
    JwtTokenProvider otherProvider = new JwtTokenProvider();
    ReflectionTestUtils.setField(
        otherProvider, "jwtSecret", "DifferentSecretKeyThatIsAlsoAtLeast32Characters");
    ReflectionTestUtils.setField(otherProvider, "jwtExpiration", TEST_EXPIRATION);

    UserPrincipal userPrincipal =
        UserPrincipal.builder()
            .userId(UUID.randomUUID())
            .username("<EMAIL>")
            .email("<EMAIL>")
            .tenantId(1L)
            .roles(Set.of(Role.USER))
            .status(UserStatus.ACTIVE)
            .build();

    String tokenWithDifferentSecret = otherProvider.generateToken(userPrincipal);

    // When/Then - Original provider cannot validate token with different secret
    assertThat(jwtTokenProvider.validateToken(tokenWithDifferentSecret)).isFalse();
  }

  @Test
  void testTokenWithAllRoles() {
    // Given - User with all roles
    UserPrincipal adminPrincipal =
        UserPrincipal.builder()
            .userId(UUID.randomUUID())
            .username("<EMAIL>")
            .email("<EMAIL>")
            .tenantId(1L)
            .roles(Set.of(Role.TENANT_ADMIN, Role.SEGMENT_ADMIN, Role.USER, Role.READONLY))
            .assignedSegments(Set.of())
            .status(UserStatus.ACTIVE)
            .build();

    // When
    String token = jwtTokenProvider.generateToken(adminPrincipal);

    // Then
    Set<Role> extractedRoles = jwtTokenProvider.getRolesFromToken(token);
    assertThat(extractedRoles).hasSize(4);
    assertThat(extractedRoles)
        .contains(Role.TENANT_ADMIN, Role.SEGMENT_ADMIN, Role.USER, Role.READONLY);
  }

  @Test
  void testTokenWithNoSegments() {
    // Given - User with no segments
    UserPrincipal userPrincipal =
        UserPrincipal.builder()
            .userId(UUID.randomUUID())
            .username("<EMAIL>")
            .email("<EMAIL>")
            .tenantId(1L)
            .roles(Set.of(Role.USER))
            .assignedSegments(Set.of()) // Empty segments
            .status(UserStatus.ACTIVE)
            .build();

    // When
    String token = jwtTokenProvider.generateToken(userPrincipal);

    // Then
    Set<UUID> extractedSegments = jwtTokenProvider.getSegmentsFromToken(token);
    assertThat(extractedSegments).isEmpty();
  }

  @Test
  void testTokenCreationTime() {
    // Given
    UserPrincipal userPrincipal =
        UserPrincipal.builder()
            .userId(UUID.randomUUID())
            .username("<EMAIL>")
            .email("<EMAIL>")
            .tenantId(1L)
            .roles(Set.of(Role.USER))
            .status(UserStatus.ACTIVE)
            .build();

    long beforeCreation = Instant.now().toEpochMilli();

    // When
    String token = jwtTokenProvider.generateToken(userPrincipal);

    long afterCreation = Instant.now().toEpochMilli();

    // Then
    Date issuedAt = jwtTokenProvider.getIssuedAtDateFromToken(token);
    assertThat(issuedAt.getTime()).isGreaterThanOrEqualTo(beforeCreation);
    assertThat(issuedAt.getTime()).isLessThanOrEqualTo(afterCreation);
  }

  @Test
  void testMultipleTenantTokens() {
    // Given - Users from different tenants
    Long tenant1 = 1L;
    Long tenant2 = 2L;

    UserPrincipal user1 =
        UserPrincipal.builder()
            .userId(UUID.randomUUID())
            .username("<EMAIL>")
            .email("<EMAIL>")
            .tenantId(tenant1)
            .roles(Set.of(Role.USER))
            .status(UserStatus.ACTIVE)
            .build();

    UserPrincipal user2 =
        UserPrincipal.builder()
            .userId(UUID.randomUUID())
            .username("<EMAIL>")
            .email("<EMAIL>")
            .tenantId(tenant2)
            .roles(Set.of(Role.USER))
            .status(UserStatus.ACTIVE)
            .build();

    // When
    String token1 = jwtTokenProvider.generateToken(user1);
    String token2 = jwtTokenProvider.generateToken(user2);

    // Then - Each token contains correct tenant information
    assertThat(jwtTokenProvider.getTenantIdFromToken(token1)).isEqualTo(tenant1);
    assertThat(jwtTokenProvider.getTenantIdFromToken(token2)).isEqualTo(tenant2);

    // Tokens should be different
    assertThat(token1).isNotEqualTo(token2);
  }
}
